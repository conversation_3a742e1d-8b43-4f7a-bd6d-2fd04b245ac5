<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Application Settings
    |--------------------------------------------------------------------------
    |
    */

    'app' => [
        'name' => env('APP_NAME', 'NPFL-PIMS'),
        'env' => env('APP_ENV', 'local'),
        'url' => env('APP_URL', 'http://localhost:8000'),
        'support_mail' => env('MAIL_SUPPORT', '<EMAIL>'),
        'locale' => env('APP_LOCALE', 'en_NG'),
        'currency' => env('APP_CURRENCY', 'NGN'),
        'date_format' => env('DATE_FORMAT', 'd/m/Y'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Reference Number Configuration
    |--------------------------------------------------------------------------
    |
    */
    'prefix' => [],


    /*
    |--------------------------------------------------------------------------
    | Payment Configuration
    |--------------------------------------------------------------------------
    |
    */
    'payment' => [
        'paystack' => [
            'public_key' => env('PAYSTACK_PUBLIC_KEY', ''),
            'secret_key' => env('PAYSTACK_SECRET_KEY', ''),
            'payment_url' => env('PAYSTACK_PAYMENT_URL', 'https://api.paystack.co'),
        ],
        'remita' => [
            'merchant_id' => env('REMITA_MERCHANT_ID', ''),
            'service_type_id' => env('REMITA_SERVICE_TYPE_ID', ''),
            'api_key' => env('REMITA_API_KEY', ''),
            'api_url' => env('REMITA_API_URL', 'https://demo.remita.net/remita/exapp/api/v1/send/api/echannelsvc'),
            'api_url_dd' => env('REMITA_API_URL', 'https://demo.remita.net/remita/exapp/api/v1/send/api/echannelsvc'),
            'default_email' => env('REMITA_DEFAULT_EMAIL', '<EMAIL>'),
        ],
        'interswitch' => [
            'merchant_code' => env('INTERSWITCH_MERCHANT_CODE', ''),
            'api_url' => env('REMITA_API_URL', 'https://demo.remita.net/remita/exapp/api/v1/send/api/echannelsvc'),
        ],
        'paydirect' => [
            'merchant_code' => env('PAYDIRECT_MECHANT_CODE', ''),
            'merchant_code1' => env('PAYDIRECT_MECHANT_CODE1', ''),
            'api_url' => env('REMITA_API_URL', 'https://demo.remita.net/remita/exapp/api/v1/send/api/echannelsvc'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Logos and Branding
    |--------------------------------------------------------------------------
    |
    */

    'branding' => [
        'npfl_logo' => env('NPFL_LOGO', 'assets/media/logos/npfl_logo_dark.png'),
        'npfl_logo_full' => env('NPFL_LOGO_FULL', 'assets/media/logos/npfl_big_logo_new.png'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Token Configuration
    |--------------------------------------------------------------------------
    |
    */

    'security' => [
        'token_expiration_hours' => env('TOKEN_EXPIRATION_HOURS', 2),
    ],

    /*
    |--------------------------------------------------------------------------
    | Mail Configuration
    |--------------------------------------------------------------------------
    |
    */

    'mail' => [
        'mailer' => env('MAIL_MAILER', 'smtp'),
        'host' => env('MAIL_HOST', 'smtp.elasticemail.com'),
        'port' => env('MAIL_PORT', 2525),
        'username' => env('MAIL_USERNAME', ''),
        'password' => env('MAIL_PASSWORD', ''),
        'encryption' => env('MAIL_ENCRYPTION', 'ssl'),
        'from_address' => env('MAIL_FROM_ADDRESS', ''),
        'from_name' => env('MAIL_FROM_NAME', env('APP_NAME', 'NPFL-PIMS')),
    ],
];
