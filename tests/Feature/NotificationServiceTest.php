<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Shared\UserManagement\Role;
use App\Models\Shared\UserManagement\UserRank;
use App\Services\NotificationService;
use App\Notifications\GenericNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class NotificationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $notificationService;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user and authenticate
        $this->user = User::factory()->create();
        $this->actingAs($this->user);

        $this->notificationService = new NotificationService();
    }

    /** @test */
    public function it_can_get_user_notifications()
    {
        // Create a notification for the user
        $this->user->notify(new GenericNotification('Test Title', 'Test Message'));

        $notifications = $this->notificationService->notifications();

        $this->assertCount(1, $notifications);
        $this->assertEquals('Test Title', $notifications->first()->data['title']);
    }

    /** @test */
    public function it_can_get_unread_notifications_count()
    {
        // Create unread notifications
        $this->user->notify(new GenericNotification('Test 1', 'Message 1'));
        $this->user->notify(new GenericNotification('Test 2', 'Message 2'));

        $count = $this->notificationService->unreadNotificationsCount();

        $this->assertEquals(2, $count);
    }

    /** @test */
    public function it_can_mark_notification_as_read()
    {
        // Create a notification
        $this->user->notify(new GenericNotification('Test Title', 'Test Message'));
        $notification = $this->user->notifications->first();

        $result = $this->notificationService->markAsRead($notification->id);

        $this->assertTrue($result);
        $this->assertNotNull($notification->fresh()->read_at);
    }

    /** @test */
    public function it_can_mark_all_notifications_as_read()
    {
        // Create multiple notifications
        $this->user->notify(new GenericNotification('Test 1', 'Message 1'));
        $this->user->notify(new GenericNotification('Test 2', 'Message 2'));

        $result = $this->notificationService->markAllAsRead();

        $this->assertTrue($result);
        $this->assertEquals(0, $this->notificationService->unreadNotificationsCount());
    }

    /** @test */
    public function it_can_send_notifications_to_roles()
    {
        Notification::fake();

        // Create users with roles
        $adminRole = Role::factory()->create(['slug' => 'admin']);
        $userRole = Role::factory()->create(['slug' => 'user']);

        $admin1 = User::factory()->create();
        $admin2 = User::factory()->create();
        $regularUser = User::factory()->create();

        $admin1->roles()->attach($adminRole);
        $admin2->roles()->attach($adminRole);
        $regularUser->roles()->attach($userRole);

        $result = $this->notificationService->sendToRoles(
            ['admin'],
            'Admin Alert',
            'This is for admins only'
        );

        $this->assertTrue($result);

        Notification::assertSentTo([$admin1, $admin2], GenericNotification::class);
        Notification::assertNotSentTo($regularUser, GenericNotification::class);
    }

    /** @test */
    public function it_can_send_notifications_to_ranks()
    {
        Notification::fake();

        // Create users with ranks
        $rank1 = UserRank::factory()->create();
        $rank2 = UserRank::factory()->create();

        $user1 = User::factory()->create(['rank_id' => $rank1->id]);
        $user2 = User::factory()->create(['rank_id' => $rank1->id]);
        $user3 = User::factory()->create(['rank_id' => $rank2->id]);

        $result = $this->notificationService->sendToRanks(
            [$rank1->id],
            'Rank Alert',
            'This is for specific rank'
        );

        $this->assertTrue($result);

        Notification::assertSentTo([$user1, $user2], GenericNotification::class);
        Notification::assertNotSentTo($user3, GenericNotification::class);
    }

    /** @test */
    public function it_can_send_notifications_to_specific_users()
    {
        Notification::fake();

        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $user3 = User::factory()->create();

        $result = $this->notificationService->sendToUsers(
            [$user1->id, $user2->id],
            'User Alert',
            'This is for specific users'
        );

        $this->assertTrue($result);

        Notification::assertSentTo([$user1, $user2], GenericNotification::class);
        Notification::assertNotSentTo($user3, GenericNotification::class);
    }

    /** @test */
    public function it_can_use_generic_send_method()
    {
        Notification::fake();

        // Create test data
        $adminRole = Role::factory()->create(['slug' => 'admin']);
        $rank = UserRank::factory()->create();

        $admin = User::factory()->create();
        $admin->roles()->attach($adminRole);

        $designatedUser = User::factory()->create(['rank_id' => $rank->id]);
        $specificUser = User::factory()->create();

        $targets = [
            'roles' => ['admin'],
            'ranks' => [$rank->id],
            'users' => [$specificUser->id]
        ];

        $result = $this->notificationService->send(
            $targets,
            'Multi-target Alert',
            'This goes to multiple target types'
        );

        $this->assertTrue($result);

        Notification::assertSentTo([$admin, $designatedUser, $specificUser], GenericNotification::class);
    }

    /** @test */
    public function it_returns_false_when_no_users_found()
    {
        $result = $this->notificationService->sendToRoles(
            ['non-existent-role'],
            'Test',
            'Test message'
        );

        $this->assertFalse($result);
    }

    /** @test */
    public function it_handles_unauthenticated_user_gracefully()
    {
        auth()->logout();
        $service = new NotificationService();

        $this->assertCount(0, $service->notifications());
        $this->assertEquals(0, $service->unreadNotificationsCount());
        $this->assertFalse($service->markAllAsRead());
    }
}
