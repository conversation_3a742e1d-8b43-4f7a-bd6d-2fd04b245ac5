<?php

namespace Tests\Feature\Http\Controllers\Shared;

use App\Models\CreatedBy;
use App\Models\Menu;
use App\Models\ModifiedBy;
use App\Models\SubModule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use JMac\Testing\Traits\AdditionalAssertions;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * @see \App\Http\Controllers\Shared\MenuController
 */
final class MenuControllerTest extends TestCase
{
    use AdditionalAssertions, RefreshDatabase, WithFaker;

    #[Test]
    public function index_behaves_as_expected(): void
    {
        $menus = Menu::factory()->count(3)->create();

        $response = $this->get(route('menus.index'));

        $response->assertOk();
        $response->assertJsonStructure([]);
    }


    #[Test]
    public function store_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\Shared\MenuController::class,
            'store',
            \App\Http\Requests\Shared\MenuStoreRequest::class
        );
    }

    #[Test]
    public function store_saves(): void
    {
        $title = fake()->sentence(4);
        $link = fake()->text();
        $order = fake()->word();
        $created_by = CreatedBy::factory()->create();
        $modified_by = ModifiedBy::factory()->create();
        $sub_module = SubModule::factory()->create();

        $response = $this->post(route('menus.store'), [
            'title' => $title,
            'link' => $link,
            'order' => $order,
            'created_by' => $created_by->id,
            'modified_by' => $modified_by->id,
            'sub_module_id' => $sub_module->id,
        ]);

        $menus = Menu::query()
            ->where('title', $title)
            ->where('link', $link)
            ->where('order', $order)
            ->where('created_by', $created_by->id)
            ->where('modified_by', $modified_by->id)
            ->where('sub_module_id', $sub_module->id)
            ->get();
        $this->assertCount(1, $menus);
        $menu = $menus->first();

        $response->assertCreated();
        $response->assertJsonStructure([]);
    }


    #[Test]
    public function show_behaves_as_expected(): void
    {
        $menu = Menu::factory()->create();

        $response = $this->get(route('menus.show', $menu));

        $response->assertOk();
        $response->assertJsonStructure([]);
    }


    #[Test]
    public function update_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\Shared\MenuController::class,
            'update',
            \App\Http\Requests\Shared\MenuUpdateRequest::class
        );
    }

    #[Test]
    public function update_behaves_as_expected(): void
    {
        $menu = Menu::factory()->create();
        $title = fake()->sentence(4);
        $link = fake()->text();
        $order = fake()->word();
        $created_by = CreatedBy::factory()->create();
        $modified_by = ModifiedBy::factory()->create();
        $sub_module = SubModule::factory()->create();

        $response = $this->put(route('menus.update', $menu), [
            'title' => $title,
            'link' => $link,
            'order' => $order,
            'created_by' => $created_by->id,
            'modified_by' => $modified_by->id,
            'sub_module_id' => $sub_module->id,
        ]);

        $menu->refresh();

        $response->assertOk();
        $response->assertJsonStructure([]);

        $this->assertEquals($title, $menu->title);
        $this->assertEquals($link, $menu->link);
        $this->assertEquals($order, $menu->order);
        $this->assertEquals($created_by->id, $menu->created_by);
        $this->assertEquals($modified_by->id, $menu->modified_by);
        $this->assertEquals($sub_module->id, $menu->sub_module_id);
    }


    #[Test]
    public function destroy_deletes_and_responds_with(): void
    {
        $menu = Menu::factory()->create();

        $response = $this->delete(route('menus.destroy', $menu));

        $response->assertNoContent();

        $this->assertModelMissing($menu);
    }
}
