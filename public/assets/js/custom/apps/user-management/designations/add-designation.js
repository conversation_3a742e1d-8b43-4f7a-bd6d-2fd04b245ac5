"use strict";
var KTUsersAddDesignation = (function () {
    const t = document.getElementById("kt_modal_add_designation"),
        e = t.querySelector("#kt_modal_add_designation_form"),
        n = new bootstrap.Modal(t);
    return {
        init: function () {
            (() => {
                var o = FormValidation.formValidation(e, {
                    fields: {
                        name: {
                            validators: {
                                notEmpty: { message: "Designation name is required" },
                            },
                        },
                        code: {
                            validators: {
                                notEmpty: { message: "Designation code is required" },
                            },
                        },
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: "",
                        }),
                    },
                });

                // Prevent default form submission
                e.addEventListener("submit", function(event) {
                    event.preventDefault();
                    console.log("Form submission prevented");
                });

                t.querySelector('[data-kt-designation-modal-action="close"]')
                    .addEventListener("click", (t) => {
                        t.preventDefault();
                        Swal.fire({
                            text: "Are you sure you would like to close?",
                            icon: "warning",
                            showCancelButton: !0,
                            buttonsStyling: !1,
                            confirmButtonText: "Yes, close it!",
                            cancelButtonText: "No, return",
                            customClass: {
                                confirmButton: "btn btn-primary",
                                cancelButton: "btn btn-active-light",
                            },
                        }).then(function (t) {
                            t.value && n.hide();
                        });
                    });
                t.querySelector('[data-kt-designation-modal-action="cancel"]')
                    .addEventListener("click", (t) => {
                        t.preventDefault();
                        Swal.fire({
                            text: "Are you sure you would like to cancel?",
                            icon: "warning",
                            showCancelButton: !0,
                            buttonsStyling: !1,
                            confirmButtonText: "Yes, cancel it!",
                            cancelButtonText: "No, return",
                            customClass: {
                                confirmButton: "btn btn-primary",
                                cancelButton: "btn btn-active-light",
                            },
                        }).then(function (t) {
                            t.value
                                ? (e.reset(), n.hide())
                                : "cancel" === t.dismiss &&
                                Swal.fire({
                                    text: "Your form has not been cancelled!.",
                                    icon: "error",
                                    buttonsStyling: !1,
                                    confirmButtonText: "Ok, got it!",
                                    customClass: {
                                        confirmButton: "btn btn-primary",
                                    },
                                });
                        });
                    });
                const r = t.querySelector('[data-kt-designation-modal-action="submit"]');
                r.addEventListener("click", function (t) {
                    t.preventDefault();
                    o && o.validate().then(function (t) {
                        if ("Valid" == t) {
                            r.setAttribute("data-kt-indicator", "on"),
                            r.disabled = !0;
                            const formData = {};
                            const elements = e.querySelectorAll("[name]");
                            elements.forEach((el) => {
                                if (el.name && el.type !== "checkbox") {
                                    formData[el.name] = el.value;
                                }
                            });
                            axios
                                .post("/user-designations", formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                    },
                                })
                                .then(function (response) {
                                    r.removeAttribute("data-kt-indicator");
                                    r.disabled = !1;
                                    Swal.fire({
                                        text: "Designation has been successfully added!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText:
                                            "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    }).then(function (t) {
                                        t.isConfirmed && n.hide();
                                        window.location.reload();
                                    });
                                })
                                .catch(function (error) {
                                    r.removeAttribute("data-kt-indicator");
                                    r.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors = error.response.data.errors;
                                        let errorList = '<ul>';
                                        for (let key in errors) {
                                            if (errors.hasOwnProperty(key)) {
                                                errors[key].forEach(msg => {
                                                    errorList += `<li>${msg}</li>`;
                                                });
                                            }
                                        }
                                        errorList += '</ul>';
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        } else {
                            Swal.fire({
                                text: "Sorry, looks like there are some errors detected, please try again.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                },
                            });
                        }
                    });
                });
            })();
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUsersAddDesignation.init();
});
