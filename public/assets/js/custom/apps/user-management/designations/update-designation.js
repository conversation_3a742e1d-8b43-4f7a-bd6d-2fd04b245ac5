"use strict";
var KTUsersUpdateDesignation = (function () {
    const t = document.getElementById("kt_update_designation"),
        e = t.querySelector("#kt_update_designation_form");
    return {
        init: function () {
            (() => {
                var o = FormValidation.formValidation(e, {
                    fields: {
                        name: {
                            validators: {
                                notEmpty: { message: "Designation name is required" },
                            },
                        },
                        code: {
                            validators: {
                                notEmpty: { message: "Designation code is required" },
                            },
                        },
                        is_active: {
                            validators: {
                                notEmpty: {
                                    message: "Status is required",
                                },
                            },
                        },
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: "",
                        }),
                    },
                });
                const i = t.querySelector('[data-kt-designations-action="submit"]');
                i.addEventListener("click", function (t) {
                    t.preventDefault();
                    o && o.validate().then(function (t) {
                        if ("Valid" == t) {
                            i.setAttribute("data-kt-indicator", "on");
                            i.disabled = !0;
                            const formData = {};
                            const elements = e.querySelectorAll("[name]");
                            formData.permissions = [];
                            elements.forEach((el) => {
                                if (el.type === "checkbox" && el.name === "permissions[]") {
                                    if (el.checked) {
                                        formData.permissions.push(el.value);
                                    }
                                } else if (el.name && el.type !== "checkbox") {
                                    formData[el.name] = el.value;
                                }
                            });
                            const designation = document.querySelector("[name=_designation]").value;
                            axios
                                .put(`/user-designations/${designation}`, formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                    },
                                })
                                .then(function (response) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    Swal.fire({
                                        text: "Form has been successfully submitted!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText:
                                            "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    }).then(function (t) {
                                        window.location.href = "/user-designations";
                                    });
                                })
                                .catch(function (error) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors =
                                            error.response.data.errors;
                                        let errorList = "<ul>";
                                        for (let key in errors) {
                                            if (
                                                errors.hasOwnProperty(
                                                    key
                                                )
                                            ) {
                                                errors[key].forEach(
                                                    (msg) => {
                                                        errorList += `<li>${msg}</li>`;
                                                    }
                                                );
                                            }
                                        }
                                        errorList += "</ul>";
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        } else {
                            Swal.fire({
                                text: "Sorry, looks like there are some errors detected, please try again.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton:
                                        "btn btn-primary",
                                },
                            });
                        }
                    });
                });
            })();
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUsersUpdateDesignation.init();
});
