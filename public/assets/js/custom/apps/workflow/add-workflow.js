"use strict";
var KTAddWorkflow = (function () {
    const t = document.getElementById("kt_modal_add_workflow"),
        e = t.querySelector("#kt_modal_add_workflow_form"),
        n = new bootstrap.Modal(t);
    return {
        init: function () {
            (() => {
                var o = FormValidation.formValidation(e, {
                    fields: {
                        menu_id: {
                            validators: {
                                notEmpty: {
                                    message: "Page is required",
                                },
                            },
                        },
                        action: {
                            validators: {
                                notEmpty: {
                                    message: "Action/Process is required",
                                },
                            },
                        },
                        stage_number: {
                            validators: {
                                notEmpty: {
                                    message: "Stage number is required",
                                },
                            },
                        },
                        stage_progress_percent: {
                            validators: {
                                notEmpty: {
                                    message: "Progress percentage is required",
                                },
                            },
                        },
                        attachment_required: {
                            validators: {
                                notEmpty: {
                                    message: "Attachment status is required",
                                },
                            },
                        },
                        due_in_days: {
                            validators: {
                                notEmpty: {
                                    message: "Due days is required",
                                },
                            },
                        },
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: "",
                        }),
                    },
                });
                t
                    .querySelector('[data-kt-workflow-modal-action="close"]')
                    .addEventListener("click", (t) => {
                        t.preventDefault(),
                            Swal.fire({
                                text: "Are you sure you would like to close?",
                                icon: "warning",
                                showCancelButton: !0,
                                buttonsStyling: !1,
                                confirmButtonText: "Yes, close it!",
                                cancelButtonText: "No, return",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                    cancelButton: "btn btn-active-light",
                                },
                            }).then(function (t) {
                                t.value && n.hide();
                            });
                    }),
                    t
                        .querySelector(
                            '[data-kt-workflow-modal-action="cancel"]'
                        )
                        .addEventListener("click", (t) => {
                            t.preventDefault(),
                                Swal.fire({
                                    text: "Are you sure you would like to cancel?",
                                    icon: "warning",
                                    showCancelButton: !0,
                                    buttonsStyling: !1,
                                    confirmButtonText: "Yes, cancel it!",
                                    cancelButtonText: "No, return",
                                    customClass: {
                                        confirmButton: "btn btn-primary",
                                        cancelButton: "btn btn-active-light",
                                    },
                                }).then(function (t) {
                                    t.value
                                        ? (e.reset(), n.hide())
                                        : "cancel" === t.dismiss &&
                                            Swal.fire({
                                                text: "Your form has not been cancelled!.",
                                                icon: "error",
                                                buttonsStyling: !1,
                                                confirmButtonText: "Ok, got it!",
                                                customClass: {
                                                    confirmButton: "btn btn-primary",
                                                },
                                            });
                                });
                        });
                const i = t.querySelector(
                    '[data-kt-workflow-modal-action="submit"]'
                );
                i.addEventListener("click", function (t) {
                    t.preventDefault();
                    o && o.validate().then(function (t) {
                        if ("Valid" == t) {
                            i.setAttribute("data-kt-indicator", "on");
                            i.disabled = !0;
                            const formData = {};
                            formData.handlers = [];
                            formData.observers = [];
                            const elements = e.querySelectorAll("[name]");
                            elements.forEach((el) => {
                                if (el.type === "checkbox" && el.name === "handlers[]") {
                                    if (el.checked) {
                                        formData.handlers.push(el.value);
                                    }
                                } else if (el.type === "checkbox" && el.name === "observers[]") {
                                    if (el.checked) {
                                        formData.observers.push(el.value);
                                    }
                                } else if (el.name && el.type !== "checkbox") {
                                    formData[el.name] = el.value;
                                }
                            });
                            axios
                                .post("/workflows", formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                    },
                                })
                                .then(function (response) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    Swal.fire({
                                        text: "Form has been successfully submitted!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText: "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    }).then(function (t) {
                                        t.isConfirmed && n.hide();
                                        window.location.reload();
                                    });
                                })
                                .catch(function (error) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors = error.response.data.errors;
                                        let errorList = '<ul>';
                                        for (let key in errors) {
                                            if (errors.hasOwnProperty(key)) {
                                                errors[key].forEach(msg => {
                                                    errorList += `<li>${msg}</li>`;
                                                });
                                            }
                                        }
                                        errorList += '</ul>';
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        } else {
                            Swal.fire({
                                text: "Sorry, looks like there are some errors detected, please try again.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                },
                            });
                        }
                    });
                });
            })();
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTAddWorkflow.init();
});
