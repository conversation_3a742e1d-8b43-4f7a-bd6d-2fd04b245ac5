"use strict";
var KTUpdatesForm = (function () {
    var t, r, n, i, a;
    return {
        init: function () {
            (a = document.querySelector("#kt_maintenance_update")) && (
                i = a.querySelector("#kt_maintenance_add_update_form"),
                r = a.querySelector('[data-kt-maintenance-update-action="reset"]'),
                t = a.querySelector('[data-kt-maintenance-update-action="submit"]'),
                new Dropzone("#kt_modal_create_ticket_attachments", {
                    url: "https://keenthemes.com/scripts/void.php",
                    paramName: "file",
                    maxFiles: 10,
                    maxFilesize: 10,
                    addRemoveLinks: !0,
                    accept: function (t, e) {
                        "justinbieber.jpg" == t.name ? e("Naha, you don't.") : e()
                    }
                }),
                n = FormValidation.formValidation(i, {
                    fields: {
                        action_id: {
                            validators: {
                                notEmpty: {
                                    message: "Action is required"
                                }
                            }
                        },
                        comments: {
                            validators: {
                                notEmpty: {
                                    message: "Comment/Note is required"
                                }
                            }
                        },
                        forward_to: {
                            validators: {
                                notEmpty: {
                                    message: "This field is required"
                                }
                            }
                        },
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger,
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: ""
                        })
                    }
                }),
                r.addEventListener("click", (t) => {
                        t.preventDefault();
                        Swal.fire({
                            text: "Are you sure you would like to reset?",
                            icon: "warning",
                            showCancelButton: !0,
                            buttonsStyling: !1,
                            confirmButtonText: "Yes, reset it!",
                            cancelButtonText: "No, return",
                            customClass: {
                                confirmButton: "btn btn-primary",
                                cancelButton: "btn btn-active-light",
                            },
                        }).then(function (t) {
                            t.value
                                ? (i.reset(), window.location.reload())
                                : "cancel" === t.dismiss &&
                                Swal.fire({
                                    text: "Your form has not been cancelled!.",
                                    icon: "error",
                                    buttonsStyling: !1,
                                    confirmButtonText: "Ok, got it!",
                                    customClass: {
                                        confirmButton: "btn btn-primary",
                                    },
                                });
                        });
                    }),
                t.addEventListener("click", (function (e) {
                    e.preventDefault(),
                        n && n.validate().then((function (e) {
                            if ("Valid" == e) {
                                t.setAttribute("data-kt-indicator", "on");
                                t.disabled = !0;
                                const formData = new FormData(i);
                                const internal = document.querySelector("[name=updatable_id]").value;
                                axios
                                    .post(`/estate/maintenance/internal/${internal}/add-update`, formData, {
                                        headers: {
                                            "X-CSRF-TOKEN":
                                                document.querySelector(
                                                    "[name=_token]"
                                                ).value,
                                            "Content-Type": "multipart/form-data",
                                            "X-HTTP-Method-Override": "PUT"
                                        },
                                    })
                                    .then(function (response) {
                                        i.removeAttribute("data-kt-indicator");
                                        i.disabled = !1;
                                        Swal.fire({
                                            text: "Entry has been successfully updated!",
                                            icon: "success",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        }).then(function (t) {
                                            window.location.href = `/estate/maintenance/internal/${internal}`;
                                        });
                                    })
                                    .catch(function (error) {
                                        i.removeAttribute("data-kt-indicator");
                                        i.disabled = !1;
                                        if (
                                            error.response &&
                                            error.response.status === 422
                                        ) {
                                            let errors =
                                                error.response.data.errors;
                                            let errorList = "<ul>";
                                            for (let key in errors) {
                                                if (errors.hasOwnProperty(key)) {
                                                    errors[key].forEach((msg) => {
                                                        errorList += `<li>${msg}</li>`;
                                                    });
                                                }
                                            }
                                            errorList += "</ul>";
                                            Swal.fire({
                                                html: errorList,
                                                icon: "error",
                                                buttonsStyling: !1,
                                                confirmButtonText:
                                                    "Ok, got it!",
                                                customClass: {
                                                    confirmButton:
                                                        "btn btn-primary",
                                                },
                                            });
                                        }
                                    });
                            } else {
                                Swal.fire({
                                    text: "Sorry, looks like there are some errors detected, please try again.",
                                    icon: "error",
                                    buttonsStyling: !1,
                                    confirmButtonText: "Ok, got it!",
                                    customClass: {
                                        confirmButton: "btn btn-primary"
                                    }
                                });
                            }
                        }
                        ))
                }
                ))
            )
        }
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUpdatesForm.init();
});
var KTUpdatesTable = (function () {
    // Define variables for table element and other utilities
    var table;

    // Function to handle row expansion/collapse
    const o = () => {
        // Get all expand row buttons
        const expandRows = table.querySelectorAll('[data-kt-updates-filter="expand_row"]');

        // Add click event listener to each expand button
        expandRows.forEach((expandRow, a) => {
            expandRow.addEventListener("click", (l) => {
                // Prevent event bubbling and default behavior
                l.stopImmediatePropagation();
                l.preventDefault();

                const s = ["isOpen", "border-bottom-0"];

                const parentRow = expandRow.closest("tr");

                const updateId = parentRow.firstElementChild.getAttribute('data-kt-update-id');
                const detailsRow = table.querySelector(`[data-kt-updates-detail-row="${updateId}"]`);

                if (parentRow.classList.contains("isOpen")) {
                    parentRow.classList.remove(...s);
                    expandRow.classList.remove("active");

                    if (detailsRow) {
                        detailsRow.classList.add('d-none');
                    }
                } else {
                    parentRow.classList.add(...s);
                    expandRow.classList.add("active");

                    if (detailsRow) {
                        detailsRow.classList.remove('d-none');
                    }
                }

                // Hide all other rows with data-kt-updates-detail-row attribute
                // const allDetailRows = table.querySelectorAll('[data-kt-updates-detail-row]');
                // allDetailRows.forEach(row => {
                //     if (row.getAttribute('data-kt-updates-detail-row') !== updateId) {
                //         row.classList.add('d-none');
                //     }
                // });

            });
        });
    };

    // Return public methods
    return {
        init: function () {
            // Initialize table if it exists
            table = document.querySelector("#kt_updates_table");
            if (table) {
                o(); // Setup expand/collapse functionality
            }
        },
    };
})();
"undefined" != typeof module && (module.exports = KTUpdatesTable),
KTUtil.onDOMContentLoaded(function () {
    KTUpdatesTable.init();
});
