"use strict";
var KTShowMaintenanceEntry = function() {
    var form, modal, submitBtn, fileInput, previewContainer, selectedFiles = [];
    return {
        init: function() {
            form = document.querySelector("#kt_maintenance_add_update_form");
            if (!form) return;
            submitBtn = form.querySelector('[data-kt-maintenance-update-action="submit"]');
            fileInput = form.querySelector('input[type="file"][name="supporting_doc_url[]"]');
            previewContainer = document.getElementById('update-supporting-docs-preview');
            // --- File input and preview logic ---
            if (fileInput) {
                fileInput.addEventListener('change', function(ev) {
                    for (var j = 0; j < ev.target.files.length; j++) {
                        selectedFiles.push(ev.target.files[j]);
                    }
                    renderPreviews();
                    fileInput.value = '';
                });
            }
            function renderPreviews() {
                if (!previewContainer) return;
                // Only render new files (existing handled by blade)
                // Remove all new file previews first
                var newFileDivs = previewContainer.querySelectorAll('.new-file-preview');
                newFileDivs.forEach(function(div) { div.remove(); });
                selectedFiles.forEach(function(file, idx) {
                    var url = URL.createObjectURL(file);
                    var wrapper = document.createElement('div');
                    wrapper.className = 'position-relative m-1 shadow-sm bg-white rounded d-flex flex-column align-items-center justify-content-center new-file-preview';
                    wrapper.style.width = '90px';
                    wrapper.style.minHeight = '90px';
                    wrapper.style.border = '1px solid #e5e5e5';
                    wrapper.style.overflow = 'hidden';
                    wrapper.style.flex = '0 0 auto';
                    var ext = file.name.split('.').pop().toLowerCase();
                    var isImage = file.type.indexOf('image/') === 0;
                    var content;
                    if (isImage) {
                        content = '<img src="' + url + '" style="width:88px;height:88px;object-fit:cover;border-radius:6px;" />';
                    } else {
                        content = '<div class="d-flex flex-column align-items-center justify-content-center" style="width:88px;height:88px;">' +
                            '<i class="ki-outline ki-file fs-2"></i>' +
                            '<span class="fs-8">' + ext.toUpperCase() + '</span>' +
                            '</div>';
                    }
                    wrapper.innerHTML =
                        content +
                        '<button type="button" class="btn btn-xs btn-danger position-absolute top-0 end-0 m-1 px-1 py-0" style="border-radius:50%;width:22px;height:22px;line-height:1;z-index:2;" data-remove-idx="' + idx + '">' +
                        '<i class="ki-outline ki-cross fs-6"></i>' +
                        '</button>';
                    previewContainer.appendChild(wrapper);
                });
                previewContainer.style.display = 'flex';
                previewContainer.style.flexWrap = 'wrap';
                previewContainer.style.gap = '12px';
            }
            if (previewContainer) {
                previewContainer.addEventListener('click', function(e) {
                    var btn = e.target.closest('button[data-remove-idx]');
                    if (btn) {
                        var idx = parseInt(btn.getAttribute('data-remove-idx'));
                        selectedFiles.splice(idx, 1);
                        renderPreviews();
                    }
                });
            }
            // --- End file preview logic ---
            // Existing doc delete logic
            document.querySelectorAll('.delete-existing-doc').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    const filename = this.getAttribute('data-filename');
                    const docDiv = this.closest('.position-relative');
                    if (!filename) return;
                    Swal.fire({
                        text: "Are you sure you want to delete this document?",
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonText: "Yes, delete it!",
                        cancelButtonText: "No, keep it",
                        customClass: {
                            confirmButton: "btn btn-danger",
                            cancelButton: "btn btn-secondary"
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            axios.post('/estate/maintenance/internal/delete-supporting-doc', {
                                maintenance_id: document.querySelector('input[name="_internal"]').value,
                                filename: filename
                            }, {
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('[name=_token]').value
                                }
                            }).then(function(response) {
                                if (response.data.success) {
                                    docDiv.remove();
                                } else {
                                    Swal.fire('Error', 'Could not delete file.', 'error');
                                }
                            }).catch(function() {
                                Swal.fire('Error', 'Could not delete file.', 'error');
                            });
                        }
                    });
                });
            });
            // Submit logic
            if (submitBtn) {
                submitBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    // Add form validation if needed
                    submitBtn.setAttribute("data-kt-indicator", "on");
                    submitBtn.disabled = !0;
                    const formData = new FormData(form);
                    // Remove any existing files in FormData
                    formData.delete('supporting_doc_url[]');
                    selectedFiles.forEach(function(file) {
                        formData.append('supporting_doc_url[]', file);
                    });
                    axios.post(window.location.pathname, formData, {
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('[name=_token]').value,
                            'Content-Type': 'multipart/form-data'
                        }
                    })
                    .then(function(response) {
                        submitBtn.removeAttribute("data-kt-indicator");
                        submitBtn.disabled = false;
                        Swal.fire({
                            text: 'Update submitted successfully!',
                            icon: 'success',
                            buttonsStyling: false,
                            confirmButtonText: 'Ok, got it!',
                            customClass: { confirmButton: 'btn btn-primary' }
                        }).then(function(t) {
                            if (t.isConfirmed) window.location.reload();
                        });
                    })
                    .catch(function(error) {
                        submitBtn.removeAttribute("data-kt-indicator");
                        submitBtn.disabled = false;
                        if (error.response && error.response.status === 422) {
                            let errors = error.response.data.errors;
                            let errorList = '<ul>';
                            for (let key in errors) {
                                if (errors.hasOwnProperty(key)) {
                                    errors[key].forEach((msg) => {
                                        errorList += `<li>${msg}</li>`;
                                    });
                                }
                            }
                            errorList += '</ul>';
                            Swal.fire({
                                html: errorList,
                                icon: 'error',
                                buttonsStyling: false,
                                confirmButtonText: 'Ok, got it!',
                                customClass: { confirmButton: 'btn btn-primary' }
                            });
                        }
                    });
                });
            }
        }
    };
}();
KTUtil.onDOMContentLoaded(function() {
    KTShowMaintenanceEntry.init();
});