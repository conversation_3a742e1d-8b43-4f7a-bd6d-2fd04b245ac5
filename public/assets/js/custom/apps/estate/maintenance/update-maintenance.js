"use strict";
var KTEditMaintenanceEntry = function() {
    var t, n, i, o, a;
    var fileInput, previewContainer, selectedFiles = [];
    return {
        init: function() {
            a = document.querySelector("#kt_update_maintenance");
            if (!a) return;
            o = new bootstrap.Modal(a);
            i = a.querySelector("#kt_update_maintenance_form");
            t = a.querySelector('[data-kt-maintenance-action="submit"]');
            // --- File input and preview logic ---
            fileInput = i.querySelector('input[name="supporting_doc_url[]"]');
            previewContainer = document.getElementById('supporting-docs-preview');
            if (fileInput) {
                fileInput.addEventListener('change', function(ev) {
                    for (var j = 0; j < ev.target.files.length; j++) {
                        selectedFiles.push(ev.target.files[j]);
                    }
                    renderPreviews();
                    fileInput.value = '';
                });
            }
            function renderPreviews() {
                if (!previewContainer) return;
                previewContainer.innerHTML = '';
                selectedFiles.forEach(function(file, idx) {
                    var url = URL.createObjectURL(file);
                    var wrapper = document.createElement('div');
                    wrapper.className = 'position-relative m-1 shadow-sm bg-white rounded d-flex flex-column align-items-center justify-content-center';
                    wrapper.style.width = '90px';
                    wrapper.style.minHeight = '90px';
                    wrapper.style.border = '1px solid #e5e5e5';
                    wrapper.style.overflow = 'hidden';
                    wrapper.style.flex = '0 0 auto';
                    var content;
                    if (file.type.indexOf('image/') === 0) {
                        content = '<img src="' + url + '" style="width:88px;height:88px;object-fit:cover;border-radius:6px;" />';
                    } else {
                        content = '<div class="d-flex flex-column align-items-center justify-content-center" style="width:88px;height:88px;">' +
                            '<i class="ki-outline ki-file fs-2"></i>' +
                            '<span class="fs-8">' + file.name.split('.').pop().toUpperCase() + '</span>' +
                            '</div>';
                    }
                    wrapper.innerHTML =
                        content +
                        '<button type="button" class="btn btn-xs btn-danger position-absolute top-0 end-0 m-1 px-1 py-0" style="border-radius:50%;width:22px;height:22px;line-height:1;z-index:2;" data-remove-idx="' + idx + '">' +
                        '<i class="ki-outline ki-cross fs-6"></i>' +
                        '</button>';
                    previewContainer.appendChild(wrapper);
                });
                previewContainer.style.display = 'flex';
                previewContainer.style.flexWrap = 'wrap';
                previewContainer.style.gap = '12px';
            }
            if (previewContainer) {
                previewContainer.addEventListener('click', function(e) {
                    var btn = e.target.closest('button[data-remove-idx]');
                    if (btn) {
                        var idx = parseInt(btn.getAttribute('data-remove-idx'));
                        selectedFiles.splice(idx, 1);
                        renderPreviews();
                    }
                });
            }
            // --- End file preview logic ---
                $(i.querySelector('[name="entry_date"]')).flatpickr({
                    enableTime: !0,
                    dateFormat: "d-M-Y"
            });
                n = FormValidation.formValidation(i, {
                    fields: {
                        entry_date: {
                            validators: {
                                notEmpty: {
                                    message: "Entry date is required"
                                }
                            }
                        },
                        requesting_officer_name: {
                            validators: {
                                notEmpty: {
                                    message: "Name is required"
                                }
                            }
                        },
                        department_id: {
                            validators: {
                                notEmpty: {
                                    message: "Department is required"
                                }
                            }
                        },
                        location: {
                            validators: {
                                notEmpty: {
                                    message: "Location is required"
                                }
                            }
                        },
                        work_type_id: {
                            validators: {
                                notEmpty: {
                                    message: "Work type is required"
                                }
                            }
                        },
                        description: {
                            validators: {
                                notEmpty: {
                                    message: "Description is required"
                                }
                            }
                        },
                        proposed_action: {
                            validators: {
                                notEmpty: {
                                    message: "Action is required"
                                }
                            }
                        },
                        cost_implication: {
                            validators: {
                                notEmpty: {
                                    message: "Cost is required"
                                }
                            }
                        },
                        checked_by: {
                            validators: {
                                notEmpty: {
                                    message: "Checked by is required"
                                }
                            }
                        },
                        "notifications[]": {
                            validators: {
                                notEmpty: {
                                    message: "Please select at least one notifications method"
                                }
                            }
                        }
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger,
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: ""
                        })
                    }
            });
            t.addEventListener("click", function(e) {
                e.preventDefault();
                n && n.validate().then(function(e) {
                        if ("Valid" == e) {
                            t.setAttribute("data-kt-indicator", "on");
                            t.disabled = !0;
                            const formData = new FormData(i);
                        // Remove any existing files in FormData
                        formData.delete('supporting_doc_url[]');
                        selectedFiles.forEach(function(file) {
                            formData.append('supporting_doc_url[]', file);
                        });
                            const internal = document.querySelector("[name=_internal]").value;
                            axios
                                .post(`/estate/maintenance/internal/${internal}`, formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                        "Content-Type": "multipart/form-data",
                                        "X-HTTP-Method-Override": "PUT"
                                    },
                                })
                                .then(function (response) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    Swal.fire({
                                        text: "Entry has been successfully updated!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText:
                                            "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    }).then(function (t) {
                                        window.location.href = '/estate/maintenance/internal';
                                    });
                                })
                                .catch(function (error) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors =
                                            error.response.data.errors;
                                        let errorList = "<ul>";
                                        for (let key in errors) {
                                            if (errors.hasOwnProperty(key)) {
                                                errors[key].forEach((msg) => {
                                                    errorList += `<li>${msg}</li>`;
                                                });
                                            }
                                        }
                                        errorList += "</ul>";
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        } else {
                            Swal.fire({
                                text: "Sorry, looks like there are some errors detected, please try again.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary"
                                }
                            });
                        }
                });
            });
        }
    };
}();
KTUtil.onDOMContentLoaded(function() {
    KTEditMaintenanceEntry.init();
});

document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.delete-existing-doc').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const filename = this.getAttribute('data-filename');
            const docDiv = this.closest('.position-relative');
            if (!filename) return;

            Swal.fire({
                text: "Are you sure you want to delete this document?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes, delete it!",
                cancelButtonText: "No, keep it",
                customClass: {
                    confirmButton: "btn btn-danger",
                    cancelButton: "btn btn-secondary"
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    axios.post('/estate/maintenance/internal/delete-supporting-doc', {
                        maintenance_id: document.querySelector('input[name="_internal"]').value,
                        filename: filename
                    }, {
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('[name=_token]').value
                        }
                    }).then(function(response) {
                        if (response.data.success) {
                            docDiv.remove();
                        } else {
                            Swal.fire('Error', 'Could not delete file.', 'error');
                        }
                    }).catch(function() {
                        Swal.fire('Error', 'Could not delete file.', 'error');
                    });
                }
            });
        });
    });
});
