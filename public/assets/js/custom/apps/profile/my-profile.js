$(document).ready(function(){
	

	$("#user_idno").css('border', '2px solid orange').end();
	$("#full_name").css('border', '2px solid orange').end();
	$("#email_add").css('border', '2px solid orange').end();
	


$('#btn_profile_update_submit').on('click', function(e) {
    e.preventDefault();  
	

		//reset form error border red colour		  		
		$("gender").css('border', '1px solid #dfdfdf').end();
		$("mstatus").css('border', '1px solid #dfdfdf').end();
		$("user_phone").css('border', '1px solid #dfdfdf').end();
		$("designation").css('border', '1px solid #dfdfdf').end();
		//$("fileM").css('border', '1px solid #dfdfdf').end();
		

		// //photo works: get image base64 (works OK) ****************************************
		// var imgpathxxxx = document.getElementById("imagexxx"); 
		// var style = window.getComputedStyle(imgpathxxxx);
		// var finalavatar = style.backgroundImage.slice(5,-2);
		// //console.log( finalavatar );
		// //console.log( style.backgroundImage.slice(5,-2) );
		// //exit;
		//var finalavatar_count = finalavatar.trim().length;
		
		
		var gender = $("#gender").val(); 
		var mstatus = $("#mstatus").val();	
		var user_phone = ($("#user_phone").val().trim().length);			
		var designation = ($("#designation").val().trim().length);	
		//fileM		(finalavatar_count <= 100) ||


 
		if ( (gender <= 0)  || (mstatus <= 0)  || (user_phone === 0) || (designation === 0) ){ 
 
				 // //imagexxx
				  // if ( finalavatar_count <= 100 ) { 
						// $("#imagexxx").css('border', '2px solid red').fadeOut().fadeIn().end();
					// } else { $("#imagexxx").css('border', '1px solid green').end(); }
				 
		  //gender
		  if ( $("#gender").val() <= 0 ) { $("#gender").css('border', '2px solid red').fadeOut().fadeIn().end(); } else { $("#gender").css('border', '1px solid green').end(); }								
		  //mstatus
		  if ( $("#mstatus").val() <= 0 ) { $("#mstatus").css('border', '2px solid red').fadeOut().fadeIn().end(); } else { $("#mstatus").css('border', '1px solid green').end(); }	
		  //phone_no
		  if ( $("#user_phone").val().trim().length === 0 ) { $("#user_phone").css('border', '2px solid red').fadeOut().fadeIn().end(); } else { $("#user_phone").css('border', '1px solid green').end(); }			  
		  //designation
		  if ( $("#designation").val().trim().length === 0 ) { $("#designation").css('border', '2px solid red').fadeOut().fadeIn().end(); } else { $("#designation").css('border', '1px solid green').end(); }	
		 

 
				//hide/reset message div
				$('#MsgDiv1').hide();

				//alert pop up
				Swal.fire({
					text: "All field(s) in red are mandatory, please enter them and try again!",
					icon: "error",
					buttonsStyling: false,
					confirmButtonText: "Ok, got it!",
					customClass: {
						confirmButton: "btn btn-danger"
					}
				}); 				

		 } 
		else 
		{
	 
	        $('#btn_profile_update_submit').html('<i class="fa fa-spinner fa-spin"></i> &nbsp; Saving . . . . Please wait.').prop('disabled', true);


			var formData = $('#profile_edit_form').serialize(); 
			//formData += '&comm_channels=' + comm_channels + '&extraValue=' + finalavatar; 

			$.ajax({
				url: '/z_modules/user/php/my-profile-actions.php?updateProfile',
				type: 'POST',
				//data: $('#profile_edit_form').serialize(), 
				data: formData,
				dataType: 'json'
			})
			.done(function(data){
                        				
                if ( data.status==='success' ) {
                    
						//alert(data.message);
						
						//
						//
						//
						//
						//try file upload ajax here (second ajax) >>>>>>>>>>>>>>>>>>>>>
										/* var form = $("#profile_edit_form").get(0);
										var formData = new FormData(form)	 
									
											$.ajax({
												url: '/z_modules/user/php/my-profile-actions.php?savePhotoFile',
												type: 'POST',
												data: formData,
												contentType: false,
												processData: false,
												dataType: 'json'
											})
											.done(function(data){                     				

												if ( data.status==='success' ) {													
														//alert(data.message);
														$('#uploadStatus').html('<p class="alert alert-success">Passport Photo File was uploaded successfully. </p>');														
												} else if ( data.status==='failed' ) 
												{ 
														//alert(data.message);
														$('#uploadStatus').html('<p class="alert alert-danger">Failed to upload the file.</p>');
												} else if ( data.status==='wrongformat' )
												{ 				
														//alert(data.message);
														$('#uploadStatus').html('<p class="alert alert-warning">Only JPEG and JPG files are allowed.</p>');
												} else if ( data.status==='nofile' ) 
												{ 
														//alert(data.message);
														$('#uploadStatus').html('<p class="alert alert-info">No file was uploaded.</p>');
												} else {				
														//alert('error');		
														$('#uploadStatus').html('<p class="alert alert-danger">Unknown error has occurred.</p>');
												}
											 }) */
						//
						//
						//
						//


						//alert pop up
						Swal.fire({
							text: "Your profile information have been updated successfully. Thank you..",
							icon: "success",
							buttonsStyling: false,
							confirmButtonText: "Ok, close me",
							customClass: {
								confirmButton: "btn btn-success"
							}
						});
					
						//$('#MsgDiv1').slideDown('slow', function(){
						//$('#MsgDiv1').html('<div class="alert alert-success"><b><span class="me-5"><i class="ki-outline ki-check-circle  fs-3x text-success"></i></span></b> <span class="text-gray-700 fw-semibold d-block fs-6">Your profile information have been saved/updated successfully. This will not submit your form for processing, to make your final submission, please click on the red button at the bottom of the page.</span> </div>');
						//});  //.delay(5000).slideUp('slow')
											
                        $('#btn_profile_update_submit').html('Save Changes').prop('disabled', false);	
                               
                } else {
					
						//alert(data.message);
						
						//alert pop up
						Swal.fire({
							text: "An error occurred  while attempting to save your record. Please contact NPFL MIS for support.",
							icon: "error",
							buttonsStyling: false,
							confirmButtonText: "Ok, close me",
							customClass: {
								confirmButton: "btn btn-danger"
							}
						});
						
					 
						//$('#MsgDiv1').slideDown('slow', function(){
						//$('#MsgDiv1').html('<div class="alert alert-success"><b><span class="me-5"><i class="ki-outline ki-check-circle  fs-3x text-success"></i></span></b> <span class="text-gray-700 fw-semibold d-block fs-6">Your profile information have been saved/updated successfully. This will not submit your form for processing, to make your final submission, please click on the red button at the bottom of the page.</span> </div>');
						//});  //.delay(5000).slideUp('slow')
                                
                        $('#btn_profile_update_submit').html('Save Changes').prop('disabled', false);					
						
                }
             })
		} 
		
	



});	


$('#btn_change_password_submit').on('click', function(e) {
    e.preventDefault();  
	

		//url token
		//const urlParams = new URLSearchParams(window.location.search);
		//const tokenValue = urlParams.get('token');
		//console.log(tokenValue);


		//reset form error border red colour		  
		$("#current_password").css('border', '1px solid #dfdfdf').end();		
        $("#first_password").css('border', '1px solid #dfdfdf').end();
        $("#confirm_password").css('border', '1px solid #dfdfdf').end();

		

		var current_password = ($("#current_password").val().trim().length);
		var first_password = ($("#first_password").val().trim().length);
        var confirm_password = ($("#confirm_password").val().trim().length);	

		//alert(first_password);
		//alert(confirm_password);	
 
		if ( (current_password === 0) ||  (first_password === 0) || (confirm_password === 0) ){ 
				 
				 //current_password
				  if ( $("#current_password").val().trim().length === 0 ) { 
						$("#current_password").css('border', '2px solid red').fadeOut().fadeIn().end();
					} else { $("#current_password").css('border', '1px solid green').end(); }				 
					
				//first_password
				  if ( $("#first_password").val().trim().length === 0 ) { 
						$("#first_password").css('border', '2px solid red').fadeOut().fadeIn().end();
					} else { $("#first_password").css('border', '1px solid green').end(); }
					
				  //confirm_password
				  if ( $("#confirm_password").val().trim().length === 0 ) { 
						$("#confirm_password").css('border', '2px solid red').fadeOut().fadeIn().end();
					} else { $("#confirm_password").css('border', '1px solid green').end(); }		
 
				//hide/reset message div
				//$('#MsgDiv1').hide();

				//alert pop up
				Swal.fire({
					text: "All field(s) in red are mandatory, please enter them and try again!",
					icon: "error",
					buttonsStyling: false,
					confirmButtonText: "Ok, got it!",
					customClass: {
						confirmButton: "btn btn-danger"
					}
				}); 				

		 } 
		else 
		{
	 
	        $('#btn_change_password_submit').html('<i class="fa fa-spinner fa-spin"></i> &nbsp; Processing . . . . Please wait.').prop('disabled', true);

			var formData = $('#profile_edit_form').serialize(); 
			//formData += '&tokenValue=' + tokenValue; 

			$.ajax({
				url: '/z_modules/user/php/my-profile-actions.php?resetPassword',
				type: 'POST',
				//data: $('#kt_new_password_form').serialize(), 
				data: formData,
				dataType: 'json'
			})
			.done(function(data){
  
                if ( data.status==='success' ) {  //success process
                    				
						//alert pop up
						Swal.fire({
							text: "Your account password have been changed successfully. Use it during your next account login.",
							icon: "success",
							buttonsStyling: false,
							confirmButtonText: "Ok, close me",
							customClass: {
								confirmButton: "btn btn-success"
							}
						});
											
                        $('#btn_change_password_submit').html('Change Password').prop('disabled', false);	
                               
                } else if ( data.status==='wrongcurrentpassword' ) //wrong current password
				{ 			
						//alert pop up
						Swal.fire({
							text: "The current account password entered is wrong. Enter your correct current password and try again.",
							icon: "warning",
							buttonsStyling: false,
							confirmButtonText: "Ok, close me",
							customClass: {
								confirmButton: "btn btn-warning"
							}
						});
											
                        $('#btn_change_password_submit').html('Change Password').prop('disabled', false);	

                } else if ( data.status==='password_mismatch' ) //paasword password_mismatch
				{ 
				 
						$("#first_password").css('border', '2px solid orange').fadeOut().fadeIn().end();
						$("#confirm_password").css('border', '2px solid orange').fadeOut().fadeIn().end();
						
						//alert pop up
						Swal.fire({
							text: "Password mismatch detected. The 2 passwords must match in both content and casing.",
							icon: "warning",
							buttonsStyling: false,
							confirmButtonText: "Ok, close me",
							customClass: {
								confirmButton: "btn btn-warning"
							}
						});
                                
                        $('#btn_change_password_submit').html('Change Password').prop('disabled', false);	
				
                } else {  //technical error

						//alert pop up
						Swal.fire({
							text: "An error has been detected. Please refer to the support team at NPFL.",
							icon: "error",
							buttonsStyling: false,
							confirmButtonText: "Ok, close me",
							customClass: {
								confirmButton: "btn btn-danger"
							}
						});
                                
                        $('#btn_change_password_submit').html('Change Password').prop('disabled', false);								
					
				}
				
				
				
				
             })
		} 
		


});	











});