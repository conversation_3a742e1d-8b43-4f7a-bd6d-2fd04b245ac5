<?php

namespace Database\Seeders;

use App\Models\Shared\Menu;
use App\Models\Shared\SubModule;
use Illuminate\Database\Seeder;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $menusBySubModules = [
            'workflows' => ['ki-outline ki-data', 'workflows.index', 0, 'workflow'],
            'user-management' => [
                'ki-outline ki-abstract-28',
                null,
                1,
                'user-management',
                [
                    'users-lists' => [null, 'users.index', 0, 'user-management'],
                    'roles-lists' => [null, 'roles.index', 1, 'user-management'],
                    'permissions' => [null, 'permissions.index', 2, 'user-management'],
                ]
            ],
            'estate' => [
                'ki-outline ki-bank',
                null,
                0,
                'estate',
                [
                    'estate-dashboard' => [null, 'estate.estate.dashboard', 0, 'estate'],
                    'maintenance' => [
                        null,
                        null,
                        1,
                        'estate',
                        [
                            'internal-maintenance' => [null, 'estate.maintenance.internal.index', 0, 'estate'],
                            'external-maintenance' => [null, 'estate.maintenance.external.index', 1, 'estate'],
                        ],
                    ],
                    'encumbrences' => [null, 'estate.encumbrences.index', 2, 'estate'],
                ],
            ],
            'tenancy' => [
                'ki-outline ki-home',
                null,
                1,
                'tenancy',
                [
                    'tenancy-dashboard' => [null, 'tenancy.index', 0, 'tenancy'],
                    'tenancy-allocation' => [null, 'tenancy-allocation.index', 1, 'tenancy'],
                    'rent-demand-notice' => [null, 'rdm.index', 2, 'tenancy'],
                    'tenancy-management' => [null, 'tenancy-management.index', 3, 'tenancy'],
                    'tenancy-registration' => [null, 'tenancy-registration.index', 4, 'tenancy'],
                ],
            ],
        ];

        foreach ($menusBySubModules as $name => $details) {
            $this->createMenu($name, $details);
        }
    }

    public function createMenu(string $name, array $details, int $parentId = null): void
    {
        $subModule = SubModule::where('code', $details[3])->first();
        $menu = Menu::updateOrCreate(
            ['title' => ucwords(string: str_replace(['-', '_'], [' ', ' & '], $name))],
            [
                'icon' => $details[0],
                'route' => $details[1],
                'order' => $details[2],
                'created_by' => 1,
                'modified_by' => 1,
                'sub_module_id' => $subModule->id,
                'parent_id' => $parentId
            ]
        );

        if (isset($details[4]) && is_array($details[4])) {
            foreach ($details[4] as $name => $detail) {
                $this->createMenu($name, $detail, $menu->id);
            }
        }
    }
}
