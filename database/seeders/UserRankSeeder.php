<?php

namespace Database\Seeders;

use App\Models\Shared\UserManagement\UserRank;
use App\Models\User;
use Illuminate\Database\Seeder;

class UserRankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ranks = [
            ['Executive Director Investment', 'EDI'],
            ['Officer Internal Client', 'OIC'],
            ['Officer External Client', 'OEC'],
            ['Architect', 'ARC'],
            ['Supervisor Risk Analyst', 'SRA'],
            ['Supervisor Estate Management', 'SEM'],
            ['Cost Engineering Specialist', 'CES'],
            ['Executive Assistant', 'EA'],
            ['Lead Tech & Engineer', 'LTE'],
            ['Civil Engineer', 'CE'],
            ['Lead Commercial', 'LC'],
            ['Manager Properties I', 'MP1'],
        ];

        foreach ($ranks as $rank) {
            UserRank::updateOrCreate(
                ['name' => $rank[0]],
                [
                    'code' => $rank[1],
                    'description' => null,
                    'is_active' => true,
                    'created_by' => 1,
                    'modified_by' => 1,
                ]
            );
        }

        $users = User::all();
        foreach ($users as $user) {
            $randomRank = UserRank::inRandomOrder()->first();
            $user->rank_id = $randomRank->id;
            $user->save();
        }
    }
}
