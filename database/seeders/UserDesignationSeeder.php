<?php

namespace Database\Seeders;

use App\Models\Shared\UserManagement\UserDesignation;
use App\Models\User;
use Illuminate\Database\Seeder;

class UserDesignationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $designations = [
            ['Architect', 'ARC'],
            ['MIS Support Personnel', 'MSP'],
            ['Supervisor', 'SUP'],
            ['Data Analyst', 'DAN'],
            ['ICT Support', 'ITS'],
        ];

        foreach ($designations as $designation) {
            UserDesignation::updateOrCreate(
                ['name' => $designation[0]],
                [
                    'code' => $designation[1],
                    'description' => null,
                    'is_active' => true,
                    'created_by' => 1,
                    'modified_by' => 1,
                ]
            );
        }

        // $users = User::where('id', '<', 6)->get();
        // foreach ($users as $user) {
        //     $randomDesignation = UserDesignation::inRandomOrder()->first();
        //     $user->designation_id = $randomDesignation->id;
        //     $user->save();
        // }
    }
}
