<?php

namespace Database\Seeders;

use App\Models\Estate\Estate\EstateAction;
use Illuminate\Database\Seeder;

class EstateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $actions = [
            ['Created', 'secondary', 'created'],
            ['Edited', 'secondary', 'edited'],
            ['Approve & escalate', 'success', 'proceed'],
            ['Approved for Review', 'success', 'proceed'],
            ['Reviewed', 'primary', 'proceed'],
            ['Keep in view', 'info', 'freeze'],
            ['Re-analyse', 'warning', 'freeze'],
            ['Reject & de-escalate', 'danger', 'rollback'],
            ['Close process', 'danger', 'freeze'],
            ['Closed/KIV', 'danger', 'freeze'],
            ['Other actions', 'muted', 'freeze'],
        ];
        foreach ($actions as $action) {
            EstateAction::updateOrCreate(
                ['name' => $action[0]],
                [
                    'color' => $action[1],
                    'status' => $action[2],
                    'description' => null,
                    'is_active' => true,
                    'created_by' => 1,
                    'modified_by' => 1,
                ]
            );
        }
    }
}
