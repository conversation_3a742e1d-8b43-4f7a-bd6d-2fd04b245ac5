<?php

namespace Database\Seeders;

use App\Models\Estate\Estate\Department;
use App\Models\Estate\Estate\WorkType;
use Illuminate\Database\Seeder;

class MaintenanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $departments = [
            'Estate',
            'Audit',
            'Accounts',
            'Pension Admin',
            'Store',
            'Supply Chain',
            'Investments',
            'Logistics',
            'MD Office',
        ];
        foreach ($departments as $index => $department) {
            Department::updateOrCreate(
                ['name' => $department],
                [
                    'description' => null,
                    'is_active' => true,
                    'created_by' => 1,
                    'modified_by' => 1,
                ]
            );
        }

        $workTypes = [
            ['Air Conditioning', 'primary'],
            ['Carpentry', 'primary'],
            ['Electrical', 'primary'],
            ['Masonry', 'success'],
            ['Mechanical', 'success'],
            ['Plumbing', 'danger'],
            ['Structural', 'danger'],
            ['Others', 'secondary'],
            ['Water Treatment', 'success'],
        ];
        foreach ($workTypes as $workType) {
            WorkType::updateOrCreate(
                ['name' => $workType[0]],
                [
                    'color' => $workType[1],
                    'description' => null,
                    'is_active' => true,
                    'created_by' => 1,
                    'modified_by' => 1,
                ]
            );
        }
    }
}
