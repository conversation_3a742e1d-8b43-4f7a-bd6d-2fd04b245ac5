<?php

namespace Database\Seeders;

use Database\Seeders\EstateSeeder;
use Database\Seeders\MaintenanceSeeder;
use Database\Seeders\MenuSeeder;
use Database\Seeders\ModulesSubModulesPermissionsSeeder;
use Database\Seeders\RoleSeeder;
use Database\Seeders\UserRankSeeder;
use Database\Seeders\UserSeeder;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            UserSeeder::class,
            RoleSeeder::class,
            ModulesSubModulesPermissionsSeeder::class,
            MenuSeeder::class,
            UserRankSeeder::class,
            EstateSeeder::class,
            MaintenanceSeeder::class,
        ]);
    }
}
