<?php

namespace Database\Seeders;

use App\Models\Shared\Module;
use App\Models\Shared\SubModule;
use App\Models\Shared\UserManagement\Permission;
use Illuminate\Database\Seeder;

class ModulesSubModulesPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $abilities = [
            'create',
            'read',
            'update',
            'delete',
        ];

        $subModulesByModules = [
            'system-administration' => [
                'user-management' => [
                    'user',
                    'role',
                    'permission',
                    'user-rank'
                ],
                'workflow' => '',
            ],
            'estate' => [
                'estate' => [
                    'estate-dashboard',
                    'internal-maintenance',
                    'external-maintenance',
                    'encumbrences'
                ],
                'tenancy' => [
                    'tenancy-dashboard',
                    'tenancy-rdm',
                    'tenancy-mgt',
                    'tenancy-regn'
                ],
                'service-provider' => [
                    'service-provider-dashboard',
                    'service-provider-regn',
                    'estate-allocation',
                ],
                'payment' => [
                    'estate-payments-dashboard',
                    'rent-payment',
                    'rent-refund',
                    'statutory-bill',
                    'insurance-premium',
                    'insurance-claim',
                ],
            ],
            'commercial' => [
                'acquisition' => [
                    'acquisition-dashboard',
                    'acquisition-management',
                ],
            ],
            'technical_engineering' => [],
            'financial-investment' => [],
            'report' => [],
        ];

        // Assuming you have a SubModule model and a relationship set up in Module
        foreach ($subModulesByModules as $moduleCode => $subModules) {
            $module = Module::updateOrCreate(
                ['code' => str_replace('_', '-', $moduleCode)],
                [
                    'name' => ucwords(string: str_replace(['-', '_'], [' ', ' & '], $moduleCode)),
                    'description' => null,
                    'is_active' => true,
                    'created_by' => 1,
                    'modified_by' => 1,
                ]
            );

            foreach ($subModules as $subModuleCode => $menus) {
                // $subModuleCode is the submodule code, $menus is an array of children
                $subModule = $module->subModules()->updateOrCreate(
                    ['code' => $subModuleCode],
                    [
                        'name' => ucwords(str_replace('-', ' ', $subModuleCode)),
                        'description' => null,
                        'is_active' => true,
                        'created_by' => 1,
                        'modified_by' => 1,
                    ]
                );

                if (is_array($menus)) {
                    foreach ($menus as $index => $menuName) {
                        foreach ($abilities as $ability) {
                            $subModule->permissions()->updateOrCreate(
                                ['action' => "{$ability}.{$menuName}"],
                                [
                                    'name' => ucwords(str_replace('.', ' ', "{$ability}.{$menuName}")),
                                    'description' => null,
                                    'is_active' => true,
                                    'created_by' => 1,
                                    'modified_by' => 1,
                                ]
                            );
                        }
                    }
                } else {
                    foreach ($abilities as $ability) {
                        $subModule->permissions()->updateOrCreate(
                            ['action' => "{$ability}.{$subModuleCode}"],
                            [
                                'name' => ucwords(str_replace('.', ' ', subject: "{$ability}.{$subModuleCode}")),
                                'description' => null,
                                'is_active' => true,
                                'created_by' => 1,
                                'modified_by' => 1,
                            ]
                        );
                    }
                }
            }
        }
    }
}
