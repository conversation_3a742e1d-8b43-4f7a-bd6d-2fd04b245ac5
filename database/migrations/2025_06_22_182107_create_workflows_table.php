<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('workflows', function (Blueprint $table) {
            $table->id();
            $table->text('action');
            $table->integer('stage_number');
            $table->integer('stage_progress_percent');
            $table->boolean('attachment_required')->default(false);
            $table->integer('due_in_days');
            $table->string('handlers');
            $table->string('observers');
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('modified_by')->constrained('users');
            $table->foreignId('menu_id')->constrained('menus');
            $table->unique(['menu_id', 'stage_number']);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workflows');
    }
};
