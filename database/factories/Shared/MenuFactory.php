<?php

namespace Database\Factories\Shared;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Shared\Menu;
use App\Models\Shared\SubModule;
use App\Models\User;

class MenuFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Menu::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'title' => fake()->sentence(4),
            'link' => fake()->text(),
            'order' => fake()->word(),
            'created_by' => User::factory(),
            'modified_by' => User::factory(),
            'sub_module_id' => SubModule::factory(),
            'parent_id' => null,
        ];
    }
}
