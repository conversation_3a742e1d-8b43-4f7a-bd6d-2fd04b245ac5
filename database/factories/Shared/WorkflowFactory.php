<?php

namespace Database\Factories\Shared;

use App\Models\Shared\Menu;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Shared\Workflow;
use App\Models\User;

class WorkflowFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Workflow::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'action' => fake()->text(),
            'stage_number' => fake()->numberBetween(-10000, 10000),
            'stage_progress_percent' => fake()->numberBetween(-10000, 10000),
            'attachment_required' => fake()->boolean(),
            'due_in_days' => fake()->numberBetween(-10000, 10000),
            'handlers' => fake()->word(),
            'observers' => fake()->word(),
            'is_active' => fake()->boolean(),
            'created_by' => User::factory(),
            'modified_by' => User::factory(),
            'menu_id' => Menu::factory(),
        ];
    }
}
