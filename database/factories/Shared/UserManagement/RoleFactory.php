<?php

namespace Database\Factories\Shared\UserManagement;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Shared\UserManagement\Role;
use App\Models\User;

class RoleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Role::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'slug' => fake()->word(),
            'guard_name' => fake()->word(),
            'description' => fake()->text(),
            'is_active' => fake()->boolean(),
            'created_by' => User::factory(),
            'modified_by' => User::factory(),
        ];
    }
}
