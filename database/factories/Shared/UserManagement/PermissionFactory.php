<?php

namespace Database\Factories\Shared\UserManagement;

use App\Models\Shared\SubModule;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Shared\UserManagement\Permission;
use App\Models\User;

class PermissionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Permission::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'action' => fake()->word(),
            'description' => fake()->text(),
            'is_active' => fake()->boolean(),
            'created_by' => User::factory(),
            'modified_by' => User::factory(),
            'sub_module_id' => SubModule::factory(),
        ];
    }
}
