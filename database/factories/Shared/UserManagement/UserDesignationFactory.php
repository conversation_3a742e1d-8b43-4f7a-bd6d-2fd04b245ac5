<?php

namespace Database\Factories\Shared\UserManagement;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Shared\UserManagement\UserDesignation;
use App\Models\User;

class UserDesignationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserDesignation::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'code' => fake()->word(),
            'description' => fake()->text(),
            'is_active' => fake()->boolean(),
            'created_by' => User::factory(),
            'modified_by' => User::factory(),
        ];
    }
}
