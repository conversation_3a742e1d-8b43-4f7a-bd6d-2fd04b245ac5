<?php

namespace App\Repositories\Shared\UserManagement;

use App\Models\Shared\UserManagement\UserDesignation;
use Illuminate\Database\Eloquent\Collection;

class UserDesignationRepository
{
    public function __construct(protected UserDesignation $designation)
    {
    }

    public function create(array $data): UserDesignation
    {
        return $this->designation->create($data);
    }

    public function update(UserDesignation $designation, array $data): UserDesignation
    {
        $designation->update($data);
        return $designation;
    }

    public function delete(UserDesignation $designation): bool
    {
        return $designation->delete();
    }

    public function getAllDesignations(): Collection
    {
        return $this->designation->with(['createdBy', 'modifiedBy'])->get();
    }

    public function getDesignationById(int $id): UserDesignation
    {
        return $this->designation->find($id);
    }
} 