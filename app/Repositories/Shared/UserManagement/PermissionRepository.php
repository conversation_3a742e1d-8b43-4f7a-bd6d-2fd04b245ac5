<?php

namespace App\Repositories\Shared\UserManagement;

use App\Models\Shared\UserManagement\Permission;
use Illuminate\Database\Eloquent\Collection;

class PermissionRepository
{
    public function __construct(protected Permission $modal)
    {
    }

    public function create(array $data): Permission
    {
        return $this->modal->create($data);
    }

    public function update(Permission $module, array $data): Permission
    {
        $module->update($data);

        return $module;
    }

    public function delete(Permission $module): bool
    {
        return $module->delete();
    }

    public function getAllPermissions(): Collection
    {
        return $this->modal->all();
    }

    public function getPermissionsWithRoles(): Collection
    {
        return $this->modal->with('roles')->get();
    }

    public function getPermissionsBySubModule(): Collection
    {
        return new Collection(
            $this->modal->with('subModule')->get()->groupBy(function ($item) {
                return $item->subModule ? $item->subModule->name : 'Unknown';
            })->map(function ($permissions) {
                return $permissions->groupBy(function ($permission) {
                    // Extract page_name from action (format: verb.page_name)
                    $parts = explode('.', $permission->action, 2);
                    return isset($parts[1]) ? $parts[1] : 'Unknown';
                });
            })->all()
        );
    }

    public function getActivePermissions(): Collection
    {
        return $this->modal->where('is_active', 1)->get();
    }

    public function getPermissionById(int $id): Permission
    {
        return $this->modal->find($id);
    }

    public function getPermissionByAction(string $action): Permission
    {
        return $this->modal->where('action', $action)->first();
    }
}
