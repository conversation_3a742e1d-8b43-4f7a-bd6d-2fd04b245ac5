<?php

namespace App\Repositories\Shared\UserManagement;

use App\Models\Shared\UserManagement\UserRank;
use Illuminate\Database\Eloquent\Collection;

class UserRankRepository
{
    public function __construct(protected UserRank $rank)
    {
    }

    public function create(array $data): UserRank
    {
        return $this->rank->create($data);
    }

    public function update(UserRank $rank, array $data): UserRank
    {
        $rank->update($data);
        return $rank;
    }

    public function delete(UserRank $rank): bool
    {
        return $rank->delete();
    }

    public function getAllRanks(): Collection
    {
        return $this->rank->with(['createdBy', 'modifiedBy'])->get();
    }

    public function getRankById(int $id): UserRank
    {
        return $this->rank->find($id);
    }
} 