<?php

namespace App\Repositories\Shared\UserManagement;

use App\Models\Shared\UserManagement\Role;
use Illuminate\Database\Eloquent\Collection;

class RoleRepository
{
    public function __construct(protected Role $modal)
    {
    }

    public function create(array $data): Role
    {
        return $this->modal->create($data);
    }

    public function update(Role $module, array $data): Role
    {
        $module->update($data);

        return $module;
    }

    public function delete(Role $module): bool
    {
        return $module->delete();
    }

    public function all(): Collection
    {
        return $this->modal->where('is_active', 1)->get();
    }

    public function getRolesWithPermissions(): Collection
    {
        return $this->modal->with('permissions')->where('is_active', 1)->get();
    }

    public function getRolesWithUsers(): Collection
    {
        return $this->modal->with('users')->where('is_active', 1)->get();
    }

    public function getRoleById(int $id): Role
    {
        return $this->modal->find($id);
    }
}
