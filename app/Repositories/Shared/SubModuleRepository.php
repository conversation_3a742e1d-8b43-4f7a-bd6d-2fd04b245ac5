<?php

namespace App\Repositories\Shared;

use App\Models\Shared\SubModule;
use Illuminate\Database\Eloquent\Collection;

class SubModuleRepository
{
    public function __construct(protected SubModule $modal)
    {
    }

    public function create(array $data): SubModule
    {
        return $this->modal->create($data);
    }

    public function update(SubModule $module, array $data): SubModule
    {
        $module->update($data);

        return $module;
    }

    public function delete(SubModule $module): bool
    {
        return $module->delete();
    }

    public function getAllSubModules(): Collection
    {
        return $this->modal->all();
    }

    public function getSubModulesWithPermissions(): Collection
    {
        return $this->modal->with('permissions')->get();
    }

    public function getActiveSubModules(): Collection
    {
        return $this->modal->with('menus')->where('is_active', 1)->get();
    }

    public function getSubModuleById(int $id): SubModule
    {
        return $this->modal->with('menu')->find($id);
    }

    public function getSubModuleByCode(string $code): SubModule
    {
        return $this->modal->with('menu')->where('code', $code)->first();
    }
}
