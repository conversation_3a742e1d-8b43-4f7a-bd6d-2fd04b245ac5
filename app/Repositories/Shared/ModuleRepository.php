<?php

namespace App\Repositories\Shared;

use App\Models\Shared\Module;
use Illuminate\Database\Eloquent\Collection;

class ModuleRepository
{
    public function __construct(protected Module $modal)
    {
    }

    public function create(array $data): Module
    {
        return $this->modal->create($data);
    }

    public function update(Module $module, array $data): Module
    {
        $module->update($data);

        return $module;
    }

    public function delete(Module $module): bool
    {
        return $module->delete();
    }

    public function getAllModules(): Collection
    {
        return $this->modal->with('subModules')->get();
    }

    public function getActiveModules(): Collection
    {
        return $this->modal->where('is_active', 1)->get();
    }

    public function getModuleById(int $id): Module
    {
        return $this->modal->find($id);
    }

    public function getModuleByCode(string $code): Module
    {
        return $this->modal->where('code', $code)->first();
    }
}
