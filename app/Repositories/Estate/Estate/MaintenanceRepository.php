<?php

namespace App\Repositories\Estate\Estate;

use App\Models\Estate\Estate\Maintenance;
use Illuminate\Database\Eloquent\Collection;

class MaintenanceRepository
{
    public function __construct(protected Maintenance $maintenance)
    {
    }

    public function create(array $data): Maintenance
    {
        return $this->maintenance->create($data);
    }

    public function update(Maintenance $maintenance, array $data): Maintenance
    {
        $maintenance->update($data);
        return $maintenance;
    }

    public function delete(Maintenance $maintenance): bool
    {
        return $maintenance->delete();
    }

    public function getAllMaintenances(): Collection
    {
        return $this->maintenance->with([
            'department',
            'workType',
            'checkedBy',
            'updates',
        ])->get();
    }

    public function getMaintenanceById(int $id): Maintenance
    {
        return $this->maintenance->find($id);
    }
}
