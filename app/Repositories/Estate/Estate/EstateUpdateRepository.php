<?php

namespace App\Repositories\Estate\Estate;

use App\Models\Estate\Estate\EstateUpdate;
use Illuminate\Database\Eloquent\Collection;

class EstateUpdateRepository
{
    public function __construct(protected EstateUpdate $update)
    {
    }

    public function create(array $data): EstateUpdate
    {
        return $this->update->create($data);
    }

    public function update(EstateUpdate $update, array $data): EstateUpdate
    {
        $update->update($data);
        return $update;
    }

    public function delete(EstateUpdate $update): bool
    {
        return $update->delete();
    }
}
