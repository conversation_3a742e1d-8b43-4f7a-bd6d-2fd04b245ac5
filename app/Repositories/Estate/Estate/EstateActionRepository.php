<?php

namespace App\Repositories\Estate\Estate;

use App\Models\Estate\Estate\EstateAction;
use Illuminate\Database\Eloquent\Collection;

class EstateActionRepository
{
    public function __construct(protected EstateAction $action)
    {
    }

    public function create(array $data): EstateAction
    {
        return $this->action->create($data);
    }

    public function update(EstateAction $action, array $data): EstateAction
    {
        $action->update($data);
        return $action;
    }

    public function delete(EstateAction $action): bool
    {
        return $action->delete();
    }

    public function getActionById(int $id): EstateAction
    {
        return $this->action->findOrFail($id);
    }

    public function getActiveActions(): Collection
    {
        return $this->action->where('is_active', true)->get();
    }

    public function getAllActions(): Collection
    {
        return $this->action->get();
    }

    public function getActionByStatus(string $status): EstateAction
    {
        return $this->action->where('status', $status)->first();
    }

    public function getActionsNotInStatuses(array $statuses): Collection
    {
        return $this->action->whereNotIn('status', ['created', 'edited', ...$statuses])->get();
    }
}
