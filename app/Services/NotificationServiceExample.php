<?php

namespace App\Services;

/**
 * Example usage of NotificationService
 * This file demonstrates how to use the NotificationService in various scenarios
 */
class NotificationServiceExample
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Example: Send notification to admin roles
     */
    public function notifyAdmins()
    {
        return $this->notificationService->sendToRoles(
            ['admin', 'super-admin'],
            'System Alert',
            'A new user has registered and requires approval.',
            ['type' => 'user_registration', 'priority' => 'high']
        );
    }

    /**
     * Example: Send notification to specific ranks
     */
    public function notifyManagers()
    {
        // Assuming rank IDs for managers
        $managerRankIds = [1, 2, 3];

        return $this->notificationService->sendToRanks(
            $managerRankIds,
            'Monthly Report Due',
            'Please submit your monthly reports by the end of this week.',
            ['deadline' => now()->addDays(7), 'type' => 'report_reminder']
        );
    }

    /**
     * Example: Send notification to specific ranks
     */
    public function notifyHighRanks()
    {
        // Assuming rank IDs for senior positions
        $seniorRankIds = [1, 2];

        return $this->notificationService->sendToRanks(
            $seniorRankIds,
            'Board Meeting Scheduled',
            'The quarterly board meeting has been scheduled for next Friday.',
            ['meeting_date' => now()->addDays(10), 'type' => 'meeting']
        );
    }

    /**
     * Example: Send notification to specific users
     */
    public function notifySpecificUsers()
    {
        $userIds = [1, 5, 10];

        return $this->notificationService->sendToUsers(
            $userIds,
            'Personal Message',
            'You have been selected for the special project.',
            ['project_id' => 123, 'type' => 'assignment']
        );
    }

    /**
     * Example: Send notification using the generic send method
     */
    public function sendComplexNotification()
    {
        $targets = [
            'roles' => ['admin', 'manager'],
            'ranks' => [1, 2],
            'users' => [5, 10]
        ];

        return $this->notificationService->send(
            $targets,
            'System Maintenance',
            'The system will be under maintenance this weekend.',
            [
                'maintenance_start' => now()->addDays(5),
                'maintenance_end' => now()->addDays(6),
                'type' => 'maintenance',
                'action_url' => url('/maintenance-info'),
                'action_text' => 'View Details'
            ]
        );
    }

    /**
     * Example: Get notification statistics
     */
    public function getNotificationStats()
    {
        return [
            'total_notifications' => $this->notificationService->notifications()->count(),
            'unread_notifications' => $this->notificationService->unreadNotificationsCount(),
            'recent_notifications' => $this->notificationService->notifications()->take(5)
        ];
    }

    /**
     * Example: Mark notifications as read
     */
    public function manageNotifications($notificationId = null)
    {
        if ($notificationId) {
            // Mark specific notification as read
            return $this->notificationService->markAsRead($notificationId);
        } else {
            // Mark all notifications as read
            return $this->notificationService->markAllAsRead();
        }
    }
}

/**
 * Usage Examples in Controllers or other classes:
 *
 * // In a controller
 * public function sendWelcomeNotification(NotificationService $notificationService)
 * {
 *     $notificationService->sendToRoles(
 *         ['user'],
 *         'Welcome to the Platform!',
 *         'Thank you for joining us. Explore the features and get started.'
 *     );
 * }
 *
 * // In an event listener
 * public function handle(SomeEvent $event)
 * {
 *     app(NotificationService::class)->sendToUsers(
 *         [$event->userId],
 *         'Event Notification',
 *         'Something important happened.'
 *     );
 * }
 *
 * // In a job or command
 * public function handle()
 * {
 *     $service = app(NotificationService::class);
 *     $service->send(
 *         ['roles' => ['admin']],
 *         'Daily Report',
 *         'Here is your daily system report.'
 *     );
 * }
 */
