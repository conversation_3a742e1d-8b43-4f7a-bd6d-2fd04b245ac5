<?php

namespace App\Services\Estate\Estate;

use App\Models\Estate\Estate\EstateAction;
use App\Models\Estate\Estate\Maintenance;
use App\Repositories\Estate\Estate\MaintenanceRepository;
use App\Services\Shared\WorkflowService;
use Illuminate\Database\Eloquent\Collection;

class MaintenanceService
{
    public function __construct(
        protected EstateActionService $actionService,
        protected MaintenanceRepository $repo,
        protected WorkflowService $workflowService
    ) {
    }

    public function create(array $data): Maintenance
    {
        return $this->repo->create($data);
    }

    public function getAllMaintenances(): Collection
    {
        return $this->repo->getAllMaintenances();
    }

    public function update(Maintenance $maintenance, array $data): Maintenance
    {
        return $this->repo->update($maintenance, $data);
    }

    public function delete(Maintenance $maintenance): bool
    {
        return $this->repo->delete($maintenance);
    }

    public function getMaintenanceById(int $id): Maintenance
    {
        return $this->repo->getMaintenanceById($id);
    }

    public function applyActionOnMaintenance(Maintenance $maintenance, int $actionId): Maintenance
    {
        /** @var EstateAction $action */
        $action = $this->actionService->getEstateActionById($actionId);

        $currentWorkflowStage = $maintenance->workflow->stage_number;

        switch ($action->status) {
            case 'proceed':
                $nextWorkflow = $this->workflowService->getWorkflowByMenuAndStage(
                    $maintenance->workflow->menu_id,
                    $currentWorkflowStage + 1
                );
                if ($nextWorkflow) {
                    $maintenance->workflow_id = $nextWorkflow->id;
                }
                break;
            case 'rollback':
                $previousWorkflow = $this->workflowService->getWorkflowByMenuAndStage(
                    $maintenance->workflow->menu_id,
                    $currentWorkflowStage - 1
                );
                if ($previousWorkflow) {
                    $maintenance->workflow_id = $previousWorkflow->id;
                }
                break;
            case 'freeze':
                // Maintain current workflow
                break;
        }

        $maintenance->save();

        return $maintenance;
    }
}
