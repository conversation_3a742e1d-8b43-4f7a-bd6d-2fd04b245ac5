<?php

namespace App\Services\Estate\Estate;

use App\Models\Estate\Estate\EstateAction;
use App\Repositories\Estate\Estate\EstateActionRepository;
use App\Services\Shared\WorkflowService;
use Illuminate\Database\Eloquent\Collection;

class EstateActionService
{
    public function __construct(
        protected EstateActionRepository $repo,
        protected WorkflowService $workflowService
    ) {
    }

    public function create(array $data): EstateAction
    {
        return $this->repo->create($data);
    }

    public function update(EstateAction $action, array $data): EstateAction
    {
        return $this->repo->update($action, $data);
    }

    public function delete(EstateAction $action): bool
    {
        return $this->repo->delete($action);
    }

    public function getAllEstateActions(): Collection
    {
        return $this->repo->getAllActions();
    }

    public function getActiveEstateActions(): Collection
    {
        return $this->repo->getActiveActions();
    }

    public function getEstateActionById(int $id): EstateAction
    {
        return $this->repo->getActionById($id);
    }

    public function getEstateActionByStatus(string $status): EstateAction
    {
        return $this->repo->getActionByStatus($status);
    }

    public function getEstateActionNotInStatuses(array $statuses): Collection
    {
        return $this->repo->getActionsNotInStatuses($statuses);
    }

    public function getActionsBasedOnWorkflowStage(int $workflowId): Collection
    {
        $workflow = $this->workflowService->getWorkflowById($workflowId);

        // First stage - exclude rollback actions
        if ($workflow->stage_number === 1) {
            return $this->getEstateActionNotInStatuses(['rollback']);
        }

        // Last stage - exclude proceed actions
        $nextWorkflow = $this->workflowService->getWorkflowByMenuAndStage(
            $workflow->menu_id,
            $workflow->stage_number + 1
        );
        if (!$nextWorkflow) {
            return $this->getEstateActionNotInStatuses(['proceed']);
        }

        // Middle stages - all active actions available
        return $this->getActiveEstateActions();
    }
}
