<?php

namespace App\Services\Estate\Estate;

use App\Models\Estate\Estate\EstateUpdate;
use App\Models\Estate\Estate\Maintenance;
use App\Models\User;
use App\Repositories\Estate\Estate\EstateUpdateRepository;
use App\Services\Estate\Estate\EstateActionService;
use Illuminate\Database\Eloquent\Collection;

class EstateUpdateService
{
    public function __construct(
        protected EstateActionService $actionService,
        protected EstateUpdateRepository $repo
    ) {
    }

    public function maintenanceCreated(Maintenance $maintenance): void
    {
        $currentUser = auth()->user();
        $action = $this->actionService->getEstateActionByStatus('created');

        $maintenance->updates()->create([
            'action_id' => $action->id,
            'comments' => 'New maintenance entry created',
            'created_by' => $currentUser->id,
            'modified_by' => $currentUser->id,
        ]);
    }

    public function maintenanceEdited(Maintenance $maintenance): void
    {
        $currentUser = auth()->user();
        $action = $this->actionService->getEstateActionByStatus('edited');

        $maintenance->updates()->create([
            'action_id' => $action->id,
            'comments' => 'Maintenance entry edited',
            'created_by' => $currentUser->id,
            'modified_by' => $currentUser->id,
        ]);
    }

    public function applyUpdateToMaintenance(Maintenance $maintenance, array $data): EstateUpdate
    {
        $currentUser = auth()->user();
        return $maintenance->updates()->create([
            ...$data,
            'created_by' => $currentUser->id,
            'modified_by' => $currentUser->id,
        ]);
    }
}
