<?php

namespace App\Services\Shared;

use App\Models\Shared\Module;
use App\Repositories\Shared\ModuleRepository;
use Illuminate\Database\Eloquent\Collection;

class ModuleService
{
    public function __construct(protected ModuleRepository $repo)
    {
    }

    public function getAllModules(): Collection
    {
        return $this->repo->getAllModules();
    }

    public function getActiveModules(): Collection
    {
        return $this->repo->getActiveModules();
    }

    public function getModuleById(int $id): Module
    {
        return $this->repo->getModuleById($id);
    }

    public function getModuleByCode(string $code): Module
    {
        return $this->repo->getModuleByCode($code);
    }
}
