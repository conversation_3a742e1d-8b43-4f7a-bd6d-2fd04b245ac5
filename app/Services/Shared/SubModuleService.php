<?php

namespace App\Services\Shared;

use App\Models\Shared\SubModule;
use App\Repositories\Shared\SubModuleRepository;
use Illuminate\Database\Eloquent\Collection;

class SubModuleService
{
    public function __construct(protected SubModuleRepository $repo)
    {
    }

    public function getAllSubModules(): Collection
    {
        return $this->repo->getAllSubModules();
    }

    public function getSubModulesWithPermissions(): Collection
    {
        return $this->repo->getSubModulesWithPermissions();
    }

    public function getActiveSubModules(): Collection
    {
        return $this->repo->getActiveSubModules();
    }

    public function getSubModuleById(int $id): SubModule
    {
        return $this->repo->getSubModuleById($id);
    }

    public function getSubModuleByCode(string $code): SubModule
    {
        return $this->repo->getSubModuleByCode($code);
    }
}
