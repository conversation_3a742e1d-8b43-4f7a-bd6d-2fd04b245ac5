<?php

namespace App\Services\Shared\UserManagement;

use App\Models\Shared\UserManagement\Permission;
use App\Repositories\Shared\UserManagement\PermissionRepository;
use Illuminate\Database\Eloquent\Collection;

class PermissionService
{
    public function __construct(protected PermissionRepository $repo)
    {
    }

    public function getAllPermissions(): Collection
    {
        return $this->repo->getAllPermissions();
    }

    public function getPermissionsWithRoles(): Collection
    {
        return $this->repo->getPermissionsWithRoles();
    }

    public function getPermissionsBySubModule(): Collection
    {
        return $this->repo->getPermissionsBySubModule();
    }

    public function getActivePermissions(): Collection
    {
        return $this->repo->getActivePermissions();
    }

    public function getPermissionById(int $id): Permission
    {
        return $this->repo->getPermissionById($id);
    }

    public function getPermissionByAction(string $action): Permission
    {
        return $this->repo->getPermissionByAction($action);
    }
}
