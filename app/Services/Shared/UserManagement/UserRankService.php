<?php

namespace App\Services\Shared\UserManagement;

use App\Models\Shared\UserManagement\UserRank;
use App\Models\User;
use App\Repositories\Shared\UserManagement\UserRankRepository;
use Illuminate\Database\Eloquent\Collection;

class UserRankService
{
    public function __construct(protected UserRankRepository $repo)
    {
    }

    public function create(array $data): UserRank
    {
        return $this->repo->create($data);
    }

    public function getAllRanks(): Collection
    {
        return $this->repo->getAllRanks();
    }

    public function update(UserRank $rank, array $data): UserRank
    {
        return $this->repo->update($rank, $data);
    }

    public function delete(UserRank $rank): bool
    {
        return $this->repo->delete($rank);
    }

    public function getRankById(int $id): UserRank
    {
        return $this->repo->getRankById($id);
    }

    public function assignRankToUsers(UserRank $rank, array $userIds): int
    {
        return User::whereIn('id', $userIds)->update(['rank_id' => $rank->id]);
    }

    public function unAssignRankFromUsers(UserRank $rank, array $userIds): int
    {
        // Ensure that only users currently assigned to this rank are unassigned
        return User::where('rank_id', $rank->id)
            ->whereIn('id', $userIds)
            ->update(['rank_id' => null]);
    }
}
