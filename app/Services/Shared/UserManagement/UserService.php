<?php

namespace App\Services\Shared\UserManagement;

use App\Models\Shared\UserManagement\Role;
use App\Models\User;
use App\Repositories\Shared\UserManagement\UserRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Hash;

class UserService
{
    public function __construct(protected UserRepository $repo)
    {
    }

    public function create(array $data): User
    {
        $data['password'] = Hash::make($data['password']);

        return $this->repo->create($data);
    }

    public function getAllUsers(): Collection
    {
        return $this->repo->getAllUsers();
    }

    public function update(User $user, array $data): User
    {
        // if (isset($data['password']) && $data['password']) {
        //     $data['password'] = Hash::make($data['password']);
        // } else {
        //     unset($data['password']);
        // }
        $updatedUser = $this->repo->update($user, $data);
        // Sync role if provided
        if (array_key_exists('role_id', $data) && $data['role_id']) {
            $this->syncRoles($updatedUser, [$data['role_id']]);
        }
        return $updatedUser;
    }

    public function delete(User $user): bool
    {
        return $this->repo->delete($user);
    }

    public function getUserById(int $id): User
    {
        return $this->repo->getUserById($id);
    }

    public function getUserByEmail(string $email): User
    {
        return $this->repo->getUserByEmail($email);
    }

    public function getUserByPhoneNo(string $phone_no): User
    {
        return $this->repo->getUserByPhoneNo($phone_no);
    }

    public function getUserByIdentifier(string $identifier): User
    {
        return $this->repo->getUserByIdentifier($identifier);
    }

    public function changeRank(User $user, int $rank_id): User
    {
        return $this->repo->changeRank($user, $rank_id);
    }

    public function getUsersByRank(int $rank_id)
    {
        return $this->repo->getUsersByRank($rank_id);
    }

    /**
     * @param User $user
     * @param array|Collection|int|Role|string $roleIds
     */
    public function syncRoles($user, $roleIds): array
    {
        return $user->roles()->sync($roleIds);
    }
}
