<?php

namespace App\Services\Shared\UserManagement;

use App\Models\Shared\UserManagement\UserDesignation;
use App\Models\User;
use App\Repositories\Shared\UserManagement\UserDesignationRepository;
use Illuminate\Database\Eloquent\Collection;

class UserDesignationService
{
    public function __construct(protected UserDesignationRepository $repo)
    {
    }

    public function create(array $data): UserDesignation
    {
        return $this->repo->create($data);
    }

    public function getAllDesignations(): Collection
    {
        return $this->repo->getAllDesignations();
    }

    public function update(UserDesignation $designation, array $data): UserDesignation
    {
        return $this->repo->update($designation, $data);
    }

    public function delete(UserDesignation $designation): bool
    {
        return $this->repo->delete($designation);
    }

    public function getDesignationById(int $id): UserDesignation
    {
        return $this->repo->getDesignationById($id);
    }

    public function assignDesignationToUsers(UserDesignation $designation, array $userIds): int
    {
        return User::whereIn('id', $userIds)->update(['designation_id' => $designation->id]);
    }

    public function unAssignDesignationFromUsers(UserDesignation $designation, array $userIds): int
    {
        // Ensure that only users currently assigned to this designation are unassigned
        return User::where('designation_id', $designation->id)
            ->whereIn('id', $userIds)
            ->update(['designation_id' => null]);
    }
}
