<?php

namespace App\Services\Shared\UserManagement;

use App\Repositories\Shared\UserManagement\RoleRepository;
use App\Models\Shared\UserManagement\Role;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Hash;

class RoleService
{
    public function __construct(protected RoleRepository $repo)
    {
    }

    public function getAllRoles(): Collection
    {
        return $this->repo->all();
    }

    public function getRolesWithPermissions(): Collection
    {
        return $this->repo->getRolesWithPermissions();
    }

    public function getRolesWithUsers(): Collection
    {
        return $this->repo->getRolesWithUsers();
    }

    public function syncPermissions(Role $role, array $permissionIds): array
    {
        return $role->permissions()->sync($permissionIds);
    }

    public function assignRoleToUsers(Role $role, array $userIds): void
    {
        $role->users()->attach($userIds);
    }

    public function unAssignRoleFromUsers(Role $role, array $userIds): int
    {
        return $role->users()->detach($userIds);
    }
}
