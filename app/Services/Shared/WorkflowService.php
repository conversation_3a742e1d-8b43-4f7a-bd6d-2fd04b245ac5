<?php

namespace App\Services\Shared;

use App\Models\Shared\Workflow;
use App\Repositories\Shared\WorkflowRepository;
use Illuminate\Database\Eloquent\Collection;

class WorkflowService
{
    public function __construct(protected WorkflowRepository $repo)
    {
    }

    public function create(array $data): Workflow
    {
        return $this->repo->create($data);
    }

    public function update(Workflow $workflow, array $data): Workflow
    {
        return $this->repo->update($workflow, $data);
    }

    public function delete(Workflow $workflow): bool
    {
        return $this->repo->delete($workflow);
    }

    public function getAllWorkflows(): Collection
    {
        return $this->repo->getAllWorkflows();
    }

    public function getActiveWorkflows(): Collection
    {
        return $this->repo->getActiveWorkflows();
    }

    public function getWorkflowById(int $id): Workflow
    {
        return $this->repo->getWorkflowById($id);
    }

    public function getWorkflowByMenuAndStage(int $menuId, int $stageNumber): Workflow|null
    {
        return $this->repo->getWorkflowByMenuAndStage($menuId, $stageNumber);
    }
}
