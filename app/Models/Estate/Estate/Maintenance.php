<?php

namespace App\Models\Estate\Estate;

use App\Models\Estate\Estate\EstateUpdate;
use App\Models\Shared\Workflow;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Maintenance extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'type',
        'entry_date',
        'requesting_officer_name',
        'department_id',
        'location',
        'work_type_id',
        'other_work_type',
        'description',
        'proposed_action',
        'cost_implication',
        'checked_by',
        'supporting_doc_url',
        'notify_by_email',
        'notify_by_phone',
        'notify_in_app',
        'workflow_id',
        'status',
        'created_by',
        'modified_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'entry_date' => 'date',
            'department_id' => 'integer',
            'work_type_id' => 'integer',
            'cost_implication' => 'float',
            'checked_by' => 'integer',
            'notify_by_email' => 'boolean',
            'notify_by_phone' => 'boolean',
            'notify_in_app' => 'boolean',
            'workflow_id' => 'integer',
            'created_by' => 'integer',
            'modified_by' => 'integer',
            'supporting_doc_url' => 'string',
        ];
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function workType(): BelongsTo
    {
        return $this->belongsTo(WorkType::class);
    }

    public function checkedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'checked_by');
    }

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'modified_by');
    }

    public function updates(): MorphMany
    {
        return $this->morphMany(EstateUpdate::class, 'updatable')->chaperone();
    }
}
