<?php

namespace App\Models\Estate\Estate;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class EstateUpdate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'action_id',
        'comments',
        'supporting_doc_url',
        'forward_to',
        'created_by',
        'modified_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'action_id' => 'integer',
            'forward_to' => 'integer',
            'created_by' => 'integer',
            'modified_by' => 'integer',
        ];
    }

    /**
     * Get the parent imageable model (Maintenace or Encumbrence).
     */
    public function updatable(): MorphTo
    {
        return $this->morphTo();
    }


    public function action(): BelongsTo
    {
        return $this->belongsTo(EstateAction::class);
    }

    public function forwardTo(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'forward_to');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'modified_by');
    }
}
