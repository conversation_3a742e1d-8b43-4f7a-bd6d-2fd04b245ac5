<?php

namespace App\Models\Shared;

use App\Http\Resources\UserCollection;
use App\Http\Resources\UserRankCollection;
use App\Models\Shared\UserManagement\UserRank;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Workflow extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'action',
        'stage_number',
        'stage_progress_percent',
        'attachment_required',
        'due_in_days',
        'handlers',
        'observers',
        'is_active',
        'created_by',
        'modified_by',
        'menu_id',
    ];

    /**PWor
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'stage_number' => 'integer',
            'stage_progress_percent' => 'integer',
            'attachment_required' => 'boolean',
            'is_active' => 'boolean',
            'created_by' => 'integer',
            'modified_by' => 'integer',
            'menu_id' => 'integer',
        ];
    }

    public function handlers(): UserRankCollection
    {
        $ids = explode(',', $this->handlers);
        $ranks = UserRank::whereIn('id', $ids)->get();
        return new UserRankCollection($ranks);
    }

    public function handlerUsers(): UserCollection
    {
        $ranks = $this->handlers()->pluck('id');
        $users = User::whereIn('rank_id', $ranks)->get();
        return new UserCollection($users);
    }

    public function observers(): UserRankCollection
    {
        $ids = explode(',', $this->observers);
        $ranks = UserRank::whereIn('id', $ids)->get();
        return new UserRankCollection($ranks);
    }

    public function observerUsers(): UserCollection
    {
        $ranks = $this->observers()->pluck('id');
        $users = User::whereIn('rank_id', $ranks)->get();
        return new UserCollection($users);
    }

    public function menu(): BelongsTo
    {
        return $this->belongsTo(Menu::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
