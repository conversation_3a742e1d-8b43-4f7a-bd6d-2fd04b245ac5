<?php

namespace App\Providers;

use App\Events\UserRegistered;
use App\Listeners\SendUserNotification;
use App\Listeners\SendWelcomeNotification;
use Illuminate\Support\ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }
    protected $listen = [
        UserRegistered::class => [
            SendUserNotification::class,
            SendWelcomeNotification::class,
        ],
    ];
    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
