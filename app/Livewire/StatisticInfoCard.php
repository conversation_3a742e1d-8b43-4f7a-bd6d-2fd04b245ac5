<?php

namespace App\Livewire;

use App\Models\Estate\Estate\Maintenance;
use Livewire\Component;

class StatisticInfoCard extends Component
{
    public string $title;
    public string $subtitle;
    public string $icon;
    public string $color;
    public ?string $type = null;

    public function mount(
        string $icon,
        string $title,
        string $color,
        ?string $type = null
    ): void {
        $this->icon = $icon;
        $this->title = $title;
        $this->color = $color;
        $this->type = $type;

        $this->subtitle = $this->resolveSubtitle();
    }

    protected function resolveSubtitle(): string
    {
        return match ($this->type) {
            'internal-maintenance-all' => (function (): string {
                return (string) Maintenance::where('type', 'internal')->count();
            })(),
            'internal-maintenance-active' =>  (function (): string {
                return (string) Maintenance::where('type', 'internal')
                    ->whereIn('status', ['pending', 'processing'])
                    ->count();
            })(),
            'internal-maintenance-percentage-completed' => (function (): string {
                $total = Maintenance::where('type', 'internal')->count();
                if ($total === 0) {
                    return '0%';
                }
                $completed = Maintenance::where('type', 'internal')
                    ->where('status', 'completed')
                    ->count();
                return (string) round(($completed / $total * 100), 2) . '%';
            })(),
            'internal-maintenance-total-cost' => (function (): string {
                $sum = Maintenance::where('type', 'internal')
                    ->get()
                    ->map(fn($maintenance) => $maintenance->cost_implication)
                    ->sum();
                return '₦ ' . number_format($sum, 2);
            })(),
            default => '0',
        };
    }

    public function render()
    {
        return view('livewire.statistic-info-card');
    }
}
