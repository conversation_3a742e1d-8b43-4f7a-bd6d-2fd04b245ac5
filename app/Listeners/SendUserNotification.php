<?php

namespace App\Listeners;

use App\Events\UserRegistered;
use App\Models\User;
use App\Notifications\NewUserNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendUserNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegistered $event): void
    {
        $admins = User::whereHas('roles', function ($query){
            $query->whereIn('slug', ['admin', 'dev']);
        })->get();

        foreach ($admins as $admin) {
            $admin->notify(new NewUserNotification($event->user));
            Log::debug('Send user notification', [$admin]);
        }
    }
}
