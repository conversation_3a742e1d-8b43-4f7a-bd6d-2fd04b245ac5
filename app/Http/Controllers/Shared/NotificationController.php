<?php

namespace App\Http\Controllers\Shared;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Mark a notification as read.
     */
    // public function markAsRead(Request $request, $id)
    // {
    //     $user = $request->user();
    //     $notification = $user->notifications()->where('id', $id)->first();
    //     if ($notification) {
    //         $notification->markAsRead();
    //         return response()->json(['success' => true]);
    //     }
    //     return response()->json(['success' => false, 'message' => 'Notification not found'], 404);
    // }

    /**
     * Mark multiple notifications as read.
     */
    public function markAsReadBulk(Request $request)
    {
        $ids = $request->input('ids', []);
        $user = $request->user();
        if (!is_array($ids) || empty($ids)) {
            return response()->json(['success' => false, 'message' => 'No notifications selected.'], 400);
        }
        $notifications = $user->notifications()->whereIn('id', $ids)->get();
        foreach ($notifications as $notification) {
            $notification->markAsRead();
        }
        return response()->json(['success' => true]);
    }
}
