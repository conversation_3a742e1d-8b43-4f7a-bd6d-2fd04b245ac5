<?php

namespace App\Http\Controllers\Shared\Auth;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Providers\RouteServiceProvider;
use Illuminate\Validation\ValidationException;

class ConfirmablePasswordController extends Controller
{
    /**
     * Show the confirm password view.
     *
     * @return \Illuminate\View\View
     */
    public function show()
    {
        return view('shared.auth.confirm-password');
    }

    /**
     * Confirm the user's password.
     *
     * @param \Illuminate\Http\Request $request
     * @return mixed
     */
    public function store(Request $request)
    {
        if (
            !Auth::guard('web')->validate([
            'email'    => $request->user()->email,
            'password' => $request->password,
            ])
        ) {
            throw ValidationException::withMessages([
                'password' => __('shared.auth.password'),
            ]);
        }

        $request->session()->put('shared.auth.password_confirmed_at', time());

        return redirect()->intended(RouteServiceProvider::HOME);
    }
}
