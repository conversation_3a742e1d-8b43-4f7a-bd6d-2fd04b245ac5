<?php

namespace App\Http\Controllers\Shared\UserManagement;

use App\Http\Controllers\Controller;
use App\Http\Requests\Shared\UserManagement\UserRankStoreRequest;
use App\Http\Requests\Shared\UserManagement\UserRankUpdateRequest;
use App\Http\Resources\UserRankResource;
use App\Models\Shared\UserManagement\UserRank;
use App\Models\User;
use App\Services\Shared\UserManagement\UserRankService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Contracts\Routing\ResponseFactory;
use Symfony\Component\HttpFoundation\Response;

class UserRankController extends Controller
{
    public function __construct(protected UserRankService $service)
    {
        $this->middleware('can:read.user-rank');
    }

    public function index(Request $request): View
    {
        $userRanks = $this->service->getAllRanks();

        return view('shared.user-management.user-rank.index', [
            'userRanks' => $userRanks,
        ]);
    }

    public function store(UserRankStoreRequest $request): Response|ResponseFactory
    {
        if (auth()->user()->cannot('create.user-rank')) {
            abort(403);
        }

        $data = $request->validated();
        $data['is_active'] = true; // Default to active
        $data['created_by'] = auth()->id();
        $data['modified_by'] = auth()->id();

        $userRank = $this->service->create($data);

        if ($userRank) {
            $request->session()->flash('success', 'Rank added successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function show(Request $request, UserRank $userRank): View
    {
        return view('shared.user-management.user-rank.show', [
            'userRank' => $userRank,
        ]);
    }

    public function edit(Request $request, UserRank $userRank): View
    {
        if (auth()->user()->cannot('update.user-rank')) {
            abort(403);
        }

        return view('shared.user-management.user-rank.edit', [
            'userRank' => $userRank->load(['users']),
        ]);
    }

    public function update(UserRankUpdateRequest $request, UserRank $userRank): Response|ResponseFactory
    {
        if (auth()->user()->cannot('update.user-rank')) {
            abort(403);
        }

        $data = $request->validated();
        $data['modified_by'] = auth()->id();

        $updatedRank = $this->service->update($userRank, $data);

        if ($updatedRank) {
            $request->session()->flash('success', 'Rank updated successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function destroy(Request $request, UserRank $userRank): RedirectResponse
    {
        if (auth()->user()->cannot('delete.user-rank')) {
            abort(403);
        }

        if ($userRank->users()->count() > 0) {
            $request->session()->flash('error', 'Rank cannot be deleted as it is assigned to users');
            return redirect()->route('user-ranks.index');
        }

        $deleted = $this->service->delete($userRank);
        if ($deleted) {
            $request->session()->flash('success', 'Rank deleted successfully');
        } else {
            $request->session()->flash('error', 'Something went wrong!');
        }
        return redirect()->route('user-ranks.index');
    }

    public function unassign(Request $request, UserRank $rank, User $user): RedirectResponse
    {
        if (auth()->user()->cannot('update.user-rank')) {
            abort(403);
        }

        $affectedRows = $this->service->unAssignRankFromUsers($rank, [$user->id]);
        if ($affectedRows > 0) {
            $request->session()->flash('success', 'User unassigned from rank successfully');
        } else {
            $request->session()->flash('error', 'Something went wrong!');
        }
        return redirect()->back();
    }

    public function unassignMany(Request $request, UserRank $rank): UserRankResource
    {
        if (auth()->user()->cannot('update.user-rank')) {
            abort(403);
        }

        $request->validate([
            'users' => ['required', 'array'],
            'users.*' => ['required', 'string', 'exists:users,id'],
        ]);

        $affectedRows = $this->service->unAssignRankFromUsers($rank, $request->users);
        if ($affectedRows > 0) {
            $request->session()->flash('success', "{$affectedRows} users unassigned from rank successfully");
        } else {
            $request->session()->flash('error', 'Something went wrong!');
        }
        return new UserRankResource($rank);
    }
}
