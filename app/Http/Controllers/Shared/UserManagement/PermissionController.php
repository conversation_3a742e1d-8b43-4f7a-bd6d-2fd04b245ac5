<?php

namespace App\Http\Controllers\Shared\UserManagement;

use App\Http\Controllers\Controller;
use App\Http\Requests\Shared\UserManagement\PermissionStoreRequest;
use App\Http\Requests\Shared\UserManagement\PermissionUpdateRequest;
use App\Models\Shared\UserManagement\Permission;
use App\Services\Shared\ModuleService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Contracts\Routing\ResponseFactory;
use Symfony\Component\HttpFoundation\Response;

class PermissionController extends Controller
{
    public function __construct(
        protected ModuleService $moduleService
    ) {
        $this->middleware('role:dev');
    }

    public function index(Request $request): View
    {
        return view('shared.user-management.permission.index', [
            'permissions' => Permission::all(),
            'modules' => $this->moduleService->getAllModules(),
        ]);
    }

    public function store(PermissionStoreRequest $request): Response|ResponseFactory
    {
        if (auth()->user()->cannot('create.permission')) {
            abort(403);
        }

        $permission = Permission::create([
            ...$request->validated(),
            'created_by' => auth()->user()->id,
            'modified_by' => auth()->user()->id,
        ]);

        if ($permission) {
            $request->session()->flash('success', 'Permission added successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function edit(Request $request, Permission $permission): View
    {
        if (auth()->user()->cannot('update.permission')) {
            abort(403);
        }

        return view('shared.user-management.permission.edit', [
            'permission' => $permission,
            'modules' => $this->moduleService->getActiveModules(),
        ]);
    }

    public function update(PermissionUpdateRequest $request, Permission $permission): Response|ResponseFactory
    {
        if (auth()->user()->cannot('update.permission')) {
            abort(403);
        }

        $updated = $permission->update($request->validated());

        if ($updated) {
            $request->session()->flash('success', 'Permission updated successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function destroy(Request $request, Permission $permission): RedirectResponse
    {
        if (auth()->user()->cannot('delete.permission')) {
            abort(403);
        }

        $permission->deleteOrFail();
        $request->session()->flash('success', 'Permission removed successfully');
        return redirect()->route('permissions.index');
    }
}
