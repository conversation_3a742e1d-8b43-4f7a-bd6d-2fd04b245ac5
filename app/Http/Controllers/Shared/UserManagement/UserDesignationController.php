<?php

namespace App\Http\Controllers\Shared\UserManagement;

use App\Http\Controllers\Controller;
use App\Http\Requests\Shared\UserManagement\UserDesignationStoreRequest;
use App\Http\Requests\Shared\UserManagement\UserDesignationUpdateRequest;
use App\Http\Resources\UserDesignationResource;
use App\Models\Shared\UserManagement\UserDesignation;
use App\Models\User;
use App\Services\Shared\UserManagement\UserDesignationService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Contracts\Routing\ResponseFactory;
use Symfony\Component\HttpFoundation\Response;

class UserDesignationController extends Controller
{
    public function __construct(protected UserDesignationService $service)
    {
        $this->middleware('can:read.user-designation');
    }

    public function index(Request $request): View
    {
        $userDesignations = $this->service->getAllDesignations();

        return view('shared.user-management.user-designation.index', [
            'userDesignations' => $userDesignations,
        ]);
    }

    public function store(UserDesignationStoreRequest $request): Response|ResponseFactory
    {
        if (auth()->user()->cannot('create.user-designation')) {
            abort(403);
        }

        $data = $request->validated();
        $data['is_active'] = true; // Default to active
        $data['created_by'] = auth()->id();
        $data['modified_by'] = auth()->id();

        $userDesignation = $this->service->create($data);

        if ($userDesignation) {
            $request->session()->flash('success', 'Designation added successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function show(Request $request, UserDesignation $userDesignation): View
    {
        return view('shared.user-management.user-designation.show', [
            'designation' => $userDesignation,
        ]);
    }

    public function edit(Request $request, UserDesignation $userDesignation): View
    {
        if (auth()->user()->cannot('update.user-designation')) {
            abort(403);
        }

        return view('shared.user-management.user-designation.edit', [
            'designation' => $userDesignation,
        ]);
    }

    public function update(UserDesignationUpdateRequest $request, UserDesignation $userDesignation): Response|ResponseFactory
    {
        if (auth()->user()->cannot('update.user-designation')) {
            abort(403);
        }

        $data = $request->validated();
        $data['modified_by'] = auth()->id();

        $updatedDesignation = $this->service->update($userDesignation, $data);

        if ($updatedDesignation) {
            $request->session()->flash('success', 'Designation updated successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function destroy(Request $request, UserDesignation $userDesignation): RedirectResponse
    {
        if (auth()->user()->cannot('delete.user-designation')) {
            abort(403);
        }

        $deleted = $this->service->delete($userDesignation);
        if ($deleted) {
            $request->session()->flash('success', 'Designation deleted successfully');
        } else {
            $request->session()->flash('error', 'Something went wrong!');
        }
        return redirect()->route('user-designations.index');
    }

    public function unassign(Request $request, UserDesignation $designation, User $user): RedirectResponse
    {
        if (auth()->user()->cannot('update.user-designation')) {
            abort(403);
        }

        $affectedRows = $this->service->unAssignDesignationFromUsers($designation, [$user->id]);
        if ($affectedRows > 0) {
            $request->session()->flash('success', 'User unassigned from designation successfully');
        } else {
            $request->session()->flash('error', 'Something went wrong!');
        }
        return redirect()->back();
    }

    public function unassignMany(Request $request, UserDesignation $designation): UserDesignationResource
    {
        if (auth()->user()->cannot('update.user-designation')) {
            abort(403);
        }

        $request->validate([
            'users' => ['required', 'array'],
            'users.*' => ['required', 'string', 'exists:users,id'],
        ]);

        $affectedRows = $this->service->unAssignDesignationFromUsers($designation, $request->users);
        if ($affectedRows > 0) {
            $request->session()->flash('success', "{$affectedRows} users unassigned from designation successfully");
        } else {
            $request->session()->flash('error', 'Something went wrong!');
        }
        return new UserDesignationResource($designation);
    }
}
