<?php

namespace App\Http\Controllers\Shared\UserManagement;

use App\Events\UserRegistered;
use App\Http\Controllers\Controller;
use App\Http\Requests\Shared\UserManagement\UserUpdateRequest;
use App\Http\Requests\Shared\UserManagement\UserStoreRequest;
use App\Models\User;
use App\Models\Shared\UserManagement\UserRank;
use App\Notifications\WelcomeUserNotification;
use App\Services\NotificationService;
use App\Services\Shared\UserManagement\RoleService;
use App\Services\Shared\UserManagement\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Illuminate\Contracts\Routing\ResponseFactory;
use Symfony\Component\HttpFoundation\Response;


class UserController extends Controller
{
    public const AVATAR_PATH = 'z_files/photos/avatars/';
    private const PER_PAGE = 25;

    public function __construct(
        protected UserService $service,
        protected NotificationService $notificationService
    ) {
        $this->middleware('role:admin|dev');
    }

    public function index(Request $request): View
    {
        return view('shared.user-management.user.index', [
            'users' => $this->service->getAllUsers(),
            'ranks' => UserRank::all(),
            'roles' => app(RoleService::class)->getAllRoles(),
            'notifications' => $this->notificationService->unreadNotifications()
        ]);
    }

    public function store(UserStoreRequest $request): Response|ResponseFactory
    {
        $data = $request->validated();

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $file = $request->file('avatar');
            $filename = uniqid('avatar_' . Str::lower($data['identifier'])) . '.' . $file->getClientOriginalExtension();
            $file->move(public_path(self::AVATAR_PATH), $filename);
            $data['profile_picture'] = $filename;
        }

        $data['password'] = bcrypt($data['password']);

        // Ensure all fields are present
        $user = User::create([
            'identifier' => $data['identifier'],
            'fullname' => $data['fullname'],
            'email' => $data['email'],
            'phone_no' => $data['phone_no'],
            'password' => $data['password'],
            'profile_picture' => $data['profile_picture'] ?? null,
            'rank_id' => $data['rank_id'] ?? null,
            'is_active' => $data['is_active'] ?? 1,
        ]);

        UserRegistered::dispatch($user);
        $user->notify(new WelcomeUserNotification());
        $this->notificationService->sendToUser($user->id, 'Notification Service!', 'Welcome to the platform!');

        if ($user) {
            $request->session()->flash('success', 'User added successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function show(Request $request, User $user): View
    {
        return view('shared.user-management.user.show', [
            'user' => $user
        ]);
    }

    public function edit(Request $request, User $user): View
    {
        return view('shared.user-management.user.edit', [
            'user' => $user,
            'ranks' => UserRank::all(),
            'roles' => app(RoleService::class)->getAllRoles(),
        ]);
    }

    public function update(UserUpdateRequest $request, User $user): Response|ResponseFactory
    {
        $data = $request->validated();

        if (isset($request->avatar_remove) && $request->avatar_remove == 1) {
            if ($user->profile_picture && file_exists(public_path(self::AVATAR_PATH . $user->profile_picture))) {
                @unlink(public_path(self::AVATAR_PATH . $user->profile_picture));
            }
            $user->profile_picture = null;
            $user->save();
        }

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Optionally delete old image
            if ($user->profile_picture && file_exists(public_path(self::AVATAR_PATH . $user->profile_picture))) {
                @unlink(public_path(self::AVATAR_PATH . $user->profile_picture));
            }
            $file = $request->file('avatar');
            $filename = uniqid('avatar_' . Str::lower($user->identifier)) . '.' . $file->getClientOriginalExtension();
            $file->move(public_path(self::AVATAR_PATH), $filename);
            $data['profile_picture'] = $filename;
        }

        // If password is blank, don't update it
        if (empty($data['password'])) {
            unset($data['password']);
        } else {
            $data['password'] = bcrypt($data['password']);
        }

        $updated = $this->service->update($user, $data);
        if ($updated) {
            $request->session()->flash('success', 'User updated successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function destroy(Request $request, User $user): RedirectResponse
    {
        $user->delete();

        return redirect()->route('users.index');
    }
}
