<?php

namespace App\Http\Controllers\Shared;

use App\Http\Controllers\Controller;
use App\Http\Requests\Shared\WorkflowStoreRequest;
use App\Http\Requests\Shared\WorkflowUpdateRequest;
use App\Http\Resources\WorkflowResource;
use App\Models\Shared\UserManagement\UserRank;
use App\Models\Shared\Workflow;
use App\Services\Shared\SubModuleService;
use App\Services\Shared\WorkflowService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\Response;

class WorkflowController extends Controller
{
    public function __construct(
        protected SubModuleService $subModuleService,
        protected WorkflowService $workflowService
    ) {
    }

    public function index(Request $request): View
    {
        if (auth()->user()->cannot('read.workflows')) {
            abort(403);
        }

        return view('shared.workflow.index', [
            'workflows' => Workflow::all(),
            'subModules' => $this->subModuleService->getActiveSubModules(),
            'ranks' => UserRank::where('is_active', true)->get(),
        ]);
    }

    public function store(WorkflowStoreRequest $request): Response|ResponseFactory
    {
        if (auth()->user()->cannot('create.workflow')) {
            abort(403);
        }

        $validatedData = $request->validated();
        $validatedData['observers'] = implode(',', $validatedData['observers']);
        $validatedData['handlers'] = implode(',', $validatedData['handlers']);
        $validatedData['is_active'] = true; // Default to active
        $validatedData['created_by'] = auth()->user()->id;
        $validatedData['modified_by'] = auth()->user()->id;
        $workflow = Workflow::create($validatedData);

        if ($workflow) {
            $request->session()->flash('success', 'Workflow added successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function edit(Request $request, Workflow $workflow): View
    {
        if (auth()->user()->cannot('update.workflow')) {
            abort(403);
        }

        return view('shared.workflow.edit', [
            'workflow' => $workflow,
            'subModules' => $this->subModuleService->getActiveSubModules(),
            'ranks' => UserRank::where('is_active', true)->get(),
        ]);
    }

    public function update(
        WorkflowUpdateRequest $request,
        Workflow $workflow
    ): Response|ResponseFactory {
        if (auth()->user()->cannot('update.workflow')) {
            abort(403);
        }

        $validatedData = $request->validated();
        $validatedData['observers'] = implode(',', $validatedData['observers']);
        $validatedData['handlers'] = implode(',', $validatedData['handlers']);
        $validatedData['modified_by'] = auth()->user()->id;

        if ($workflow->update($validatedData)) {
            $request->session()->flash('success', 'Workflow updated successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function destroy(Request $request, Workflow $workflow): RedirectResponse
    {
        if (auth()->user()->cannot('delete.workflow')) {
            abort(403);
        }

        $workflow->deleteOrFail();
        $request->session()->flash('success', 'Workflow removed successfully');
        return redirect()->route('workflows.index');
    }

    public function getActors(Request $request): Response|ResponseFactory
    {
        if (auth()->user()->cannot('create.workflow')) {
            abort(403);
        }

        $data = $request->validate([
            'workflow_id' => ['required', 'integer', 'exists:workflows,id'],
        ]);

        $workflow = $this->workflowService->getWorkflowById($data['workflow_id']);
        if ($workflow) {
            return response()->json($workflow->handlerUsers()->toArray($request));
        } else {
            return $this->error('Could not fetch actors. Please try again!');
        }
    }
}
