<?php

namespace App\Http\Controllers\Shared;

use App\Http\Controllers\Controller;
use App\Http\Requests\Shared\ModuleStoreRequest;
use App\Http\Requests\Shared\ModuleUpdateRequest;
use App\Models\Shared\Module;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ModuleController extends Controller
{
    public function index(Request $request): View
    {
        $modules = Module::all();

        return view('shared.module.index', [
            'modules' => $modules,
        ]);
    }

    public function create(Request $request): View
    {
        return view('shared.module.create');
    }

    public function store(ModuleStoreRequest $request): RedirectResponse
    {
        $module = Module::create($request->validated());

        $request->session()->flash('module.id', $module->id);

        return redirect()->route('modules.index');
    }

    public function show(Request $request, Module $module): View
    {
        return view('shared.module.show', [
            'module' => $module,
        ]);
    }

    public function edit(Request $request, Module $module): View
    {
        return view('shared.module.edit', [
            'module' => $module,
        ]);
    }

    public function update(ModuleUpdateRequest $request, Module $module): RedirectResponse
    {
        $module->update($request->validated());

        $request->session()->flash('module.id', $module->id);

        return redirect()->route('modules.index');
    }

    public function destroy(Request $request, Module $module): RedirectResponse
    {
        $module->delete();

        return redirect()->route('modules.index');
    }
}
