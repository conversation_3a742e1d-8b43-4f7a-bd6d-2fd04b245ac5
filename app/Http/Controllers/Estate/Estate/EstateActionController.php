<?php

namespace App\Http\Controllers\Estate\Estate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Estate\Estate\EstateActionStoreRequest;
use App\Http\Requests\Estate\Estate\EstateActionUpdateRequest;
use App\Http\Resources\Estate\Estate\EstateActionCollection;
use App\Http\Resources\Estate\Estate\EstateActionResource;
use App\Models\Estate\Estate\EstateAction;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EstateActionController extends Controller
{
    public function index(Request $request): EstateActionCollection
    {
        $estateActions = EstateAction::all();

        return new EstateActionCollection($estateActions);
    }

    public function store(EstateActionStoreRequest $request): EstateActionResource
    {
        $estateAction = EstateAction::create($request->validated());

        return new EstateActionResource($estateAction);
    }

    public function show(Request $request, EstateAction $estateAction): EstateActionResource
    {
        return new EstateActionResource($estateAction);
    }

    public function update(EstateActionUpdateRequest $request, EstateAction $estateAction): EstateActionResource
    {
        $estateAction->update($request->validated());

        return new EstateActionResource($estateAction);
    }

    public function destroy(Request $request, EstateAction $estateAction): Response
    {
        $estateAction->delete();

        return response()->noContent();
    }
}
