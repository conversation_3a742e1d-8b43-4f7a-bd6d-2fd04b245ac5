<?php

namespace App\Http\Controllers\Estate\Estate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Estate\Estate\WorkTypeStoreRequest;
use App\Http\Requests\Estate\Estate\WorkTypeUpdateRequest;
use App\Http\Resources\Estate\Estate\WorkTypeCollection;
use App\Http\Resources\Estate\Estate\WorkTypeResource;
use App\Models\Estate\Estate\WorkType;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class WorkTypeController extends Controller
{
    public function index(Request $request): WorkTypeCollection
    {
        $workTypes = WorkType::all();

        return new WorkTypeCollection($workTypes);
    }

    public function store(WorkTypeStoreRequest $request): WorkTypeResource
    {
        $workType = WorkType::create($request->validated());

        return new WorkTypeResource($workType);
    }

    public function show(Request $request, WorkType $workType): WorkTypeResource
    {
        return new WorkTypeResource($workType);
    }

    public function update(WorkTypeUpdateRequest $request, WorkType $workType): WorkTypeResource
    {
        $workType->update($request->validated());

        return new WorkTypeResource($workType);
    }

    public function destroy(Request $request, WorkType $workType): Response
    {
        $workType->delete();

        return response()->noContent();
    }
}
