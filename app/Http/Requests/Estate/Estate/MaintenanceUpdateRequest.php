<?php

namespace App\Http\Requests\Estate\Estate;

use Illuminate\Foundation\Http\FormRequest;

class MaintenanceUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'type' => ['required', 'in:internal,external'],
            'entry_date' => ['required'],
            'requesting_officer_name' => ['required', 'string'],
            'department_id' => ['required', 'integer', 'exists:departments,id'],
            'location' => ['required', 'string'],
            'work_type_id' => ['required', 'integer', 'exists:work_types,id'],
            'other_work_type' => ['nullable', 'string'],
            'description' => ['required', 'string'],
            'proposed_action' => ['required', 'string'],
            'cost_implication' => ['required', 'numeric'],
            'checked_by' => ['required', 'integer', 'exists:users,id'],
            'supporting_doc_url' => ['nullable', 'array'],
            'supporting_doc_url.*' => ['file', 'mimes:png,jpg,jpeg,pdf', 'max:2048'],
            'notifications' => ['required', 'array'],
        ];
    }

    public function messages(): array
    {
        return [
            'notifications.required' => 'Please select at least one notifications method.',
            'notifications.array' => 'Notifications must be provided as an array.',
        ];
    }
}
