<?php

namespace App\Http\Requests\Estate\Estate;

use Illuminate\Foundation\Http\FormRequest;

class EstateActionStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'unique:estate_actions,name'],
            'color' => ['required', 'string'],
            'status' => ['required', 'in:proceed,rollback,freeze,completed'],
            'description' => ['nullable', 'string'],
        ];
    }
}
