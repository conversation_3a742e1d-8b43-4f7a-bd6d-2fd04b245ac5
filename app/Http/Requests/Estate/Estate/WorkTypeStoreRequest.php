<?php

namespace App\Http\Requests\Estate\Estate;

use Illuminate\Foundation\Http\FormRequest;

class WorkTypeStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'unique:work_types,name'],
            'color' => ['required', 'string'],
            'description' => ['nullable', 'string'],
        ];
    }
}
