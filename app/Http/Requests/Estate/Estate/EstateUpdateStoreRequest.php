<?php

namespace App\Http\Requests\Estate\Estate;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EstateUpdateStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'updatable_id' => ['required', 'integer', 'exists:maintenances,id'],
            'action_id' => ['required', 'integer', 'exists:estate_actions,id'],
            'comments' => ['required', 'string'],
            'supporting_doc_url' => ['nullable', 'array'],
            'supporting_doc_url.*' => ['file', 'mimes:png,jpg,jpeg,pdf', 'max:2048'],
            'forward_to' => ['nullable', 'integer', 'exists:users,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'updatable_id.required' => 'A maintenance record is required.',
            'updatable_id.integer' => 'Invalid maintenance record format.',
            'updatable_id.exists' => 'Selected maintenance record does not exist.',

            'action_id.required' => 'An action is required.',
            'action_id.integer' => 'Invalid action format.',
            'action_id.exists' => 'Selected action does not exist.',

            'comments.required' => 'Comments/Notes are required.',
            'comments.string' => 'Comments/Notes must be text.',

            'supporting_doc_url.array' => 'Supporting documents must be provided as an array.',
            'supporting_doc_url.*.file' => 'Each supporting document must be a valid file.',
            'supporting_doc_url.*.mimes' => 'Supporting documents must be PNG, JPG, JPEG, or PDF files.',
            'supporting_doc_url.*.max' => 'Supporting documents must not exceed 2MB in size.',

            'forward_to.integer' => 'Invalid user format.',
            'forward_to.exists' => 'Selected user does not exist.',
        ];
    }
}
