<?php

namespace App\Http\Requests\Shared\UserManagement;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserRankUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rankId = $this->route('user_rank')->id;

        return [
            'name' => ['required', 'string', Rule::unique('user_ranks', 'name')->ignore($rankId)],
            'code' => ['required', 'string', Rule::unique('user_ranks', 'code')->ignore($rankId)],
            'description' => ['nullable', 'string'],
            'is_active' => ['required'],
        ];
    }
}
