<?php

namespace App\Http\Requests\Shared\UserManagement;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserDesignationUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $designationId = $this->route('user_designation')->id;

        return [
            'name' => ['required', 'string', Rule::unique('user_designations', 'name')->ignore($designationId)],
            'code' => ['required', 'string', Rule::unique('user_designations', 'code')->ignore($designationId)],
            'description' => ['nullable', 'string'],
            'is_active' => ['required'],
        ];
    }
}
