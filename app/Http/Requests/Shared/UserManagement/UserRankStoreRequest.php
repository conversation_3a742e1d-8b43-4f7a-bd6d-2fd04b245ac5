<?php

namespace App\Http\Requests\Shared\UserManagement;

use Illuminate\Foundation\Http\FormRequest;

class UserRankStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'unique:user_ranks,name'],
            'code' => ['required', 'string', 'unique:user_ranks,code'],
            'description' => ['nullable', 'string'],
        ];
    }
}
