<?php

namespace App\Http\Requests\Shared;

use Illuminate\Foundation\Http\FormRequest;

class SubModuleStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'unique:sub_modules,name'],
            'description' => ['nullable', 'string'],
            'is_active' => ['required'],
            'code' => ['required', 'string', 'unique:sub_modules,code'],
            'module_id' => ['required', 'integer', 'exists:modules,id'],
        ];
    }
}
