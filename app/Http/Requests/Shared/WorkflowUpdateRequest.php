<?php

namespace App\Http\Requests\Shared;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WorkflowUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'action' => ['required', 'string'],
            'stage_number' => [
                'required', // The field must be present in the request
                'integer', // The value must be an integer
                Rule::unique('workflows', 'stage_number') // Must be unique in workflows table's stage_number column
                    ->where('menu_id', $this->menu_id) // But only unique within the same menu_id
                    ->ignore($this->workflow) // Ignore the current workflow when checking uniqueness (for updates)
            ],
            'stage_progress_percent' => ['required', 'integer', 'between:0,100'],
            'attachment_required' => ['required'],
            'due_in_days' => ['required', 'integer'],
            'is_active' => ['required'],
            'handlers' => ['required', 'array'],
            'handlers.*' => ['required', 'string', 'exists:user_ranks,id'],
            'observers' => ['required', 'array'],
            'observers.*' => ['required', 'string', 'exists:user_ranks,id'],
            'menu_id' => ['required', 'integer', 'exists:menus,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'handlers.required' => 'At least one handler is required.',
            'handlers.array' => 'Handlers must be provided as an array.',
            'handlers.*.required' => 'Each handler is required.',
            'handlers.*.string' => 'Each handler must be a valid string.',
            'handlers.*.exists' => 'Selected handler is invalid.',

            'observers.required' => 'At least one observer is required.',
            'observers.array' => 'Observers must be provided as an array.',
            'observers.*.required' => 'Each observer is required.',
            'observers.*.string' => 'Each observer must be a valid string.',
            'observers.*.exists' => 'Selected observer is invalid.',
        ];
    }
}
