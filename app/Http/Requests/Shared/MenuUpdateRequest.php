<?php

namespace App\Http\Requests\Shared;

use Illuminate\Foundation\Http\FormRequest;

class MenuUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string'],
            'icon' => ['required', 'string'],
            'route' => ['required', 'string'],
            'order' => ['required', 'string'],
            'created_by' => ['required', 'integer'],
            'modified_by' => ['required', 'integer'],
            'sub_module_id' => ['required', 'integer', 'exists:sub_modules,id'],
            'parent_id' => ['nullable', 'integer', 'exists:menus,id'],
        ];
    }
}
