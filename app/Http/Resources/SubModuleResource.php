<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubModuleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'is_active' => $this->is_active,
            'code' => $this->code,
            'created_by' => new UserResource($this->createdBy),
            'modified_by' => new UserResource($this->modifiedBy),
            'module' => new ModuleResource($this->module),
            'permissions' => PermissionCollection::make($this->permissions),
            'menus' => WorkflowCollection::make($this->menus),
        ];
    }
}
