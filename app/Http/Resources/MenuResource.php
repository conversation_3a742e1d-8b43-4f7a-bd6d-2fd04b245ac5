<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MenuResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'icon' => $this->icon,
            'route' => $this->route,
            'order' => $this->order,
            'is_hidden' => $this->is_hidden,
            'created_by' => new UserResource($this->createdBy),
            'modified_by' => new UserResource($this->modifiedBy),
            'sub_module' => new SubModuleResource($this->subModule),
            'parent' => new MenuResource($this->parent),
            'children' => MenuCollection::make($this->children),
        ];
    }
}
