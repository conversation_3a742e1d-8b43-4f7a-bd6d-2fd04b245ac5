<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'guard_name' => $this->guard_name,
            'description' => $this->description,
            'is_active' => $this->is_active,
            'created_by' => new UserResource($this->createdBy),
            'modified_by' => new UserResource($this->modifiedBy),
            'permissions' => PermissionCollection::make($this->permissions),
            'users' => UserCollection::make($this->users),
        ];
    }
}
