<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PermissionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'action' => $this->action,
            'description' => $this->description,
            'is_active' => $this->is_active,
            'created_by' => new UserResource($this->createdBy),
            'modified_by' => new UserResource($this->modifiedBy),
            'sub_module_id' => $this->sub_module_id,
        ];
    }
}
