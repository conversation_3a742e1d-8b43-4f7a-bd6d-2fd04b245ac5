<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class WorkflowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'action' => $this->action,
            'stage_number' => $this->stage_number,
            'stage_progress_percent' => $this->stage_progress_percent,
            'attachment_required' => $this->attachment_required,
            'due_in_days' => $this->due_in_days,
            'handlers' => $this->handlers,
            'observers' => $this->observers,
            'is_active' => $this->is_active,
            'created_by' => new UserResource($this->createdBy),
            'modified_by' => new UserResource($this->modifiedBy),
            'menu' => new MenuResource($this->menu),
        ];
    }
}
