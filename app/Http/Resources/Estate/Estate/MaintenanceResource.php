<?php

namespace App\Http\Resources\Estate\Estate;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MaintenanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'entry_date' => $this->entry_date,
            'requesting_officer_name' => $this->requesting_officer_name,
            'department_id' => $this->department_id,
            'location' => $this->location,
            'work_type_id' => $this->work_type_id,
            'other_work_type' => $this->other_work_type,
            'description' => $this->description,
            'proposed_action' => $this->proposed_action,
            'cost_implication' => $this->cost_implication,
            'checked_by' => $this->checked_by,
            'supporting_doc_url' => $this->supporting_doc_url,
            'notify_by_email' => $this->notify_by_email,
            'notify_by_phone' => $this->notify_by_phone,
            'notify_in_app' => $this->notify_in_app,
            'workflow_id' => $this->workflow_id,
            'status' => $this->status,
            'created_by' => $this->created_by,
            'modified_by' => $this->modified_by,
        ];
    }
}
