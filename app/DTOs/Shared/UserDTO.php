<?php

namespace App\DTOs;

class UserDTO
{
    public function __construct(
        public readonly string $name,
        public readonly string $identifier,
        public readonly string $fullname,
        public readonly string $phone_no,
        public readonly string $email,
        public readonly string $password,
    ) {}

    public static function fromRequest($request): self
    {
        return new self(
            $request->name,
            $request->identifier,
            $request->fullname,
            $request->phone_no,
            $request->email,
            $request->password,
        );
    }

    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'identifier' => $this->identifier,
            'fullname' => $this->fullname,
            'phone_no' => $this->phone_no,
            'email' => $this->email,
            'password' => $this->password,
        ];
    }
}
