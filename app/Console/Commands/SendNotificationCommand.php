<?php

namespace App\Console\Commands; 

use App\Services\NotificationService;
use Illuminate\Console\Command;

class SendNotificationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:send
                            {--type=roles : Type of target (roles, ranks, users)}
                            {--targets=* : Target values (role slugs, IDs, etc.)}
                            {--title= : Notification title}
                            {--message= : Notification message (optional)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send notifications to users based on roles, ranks, or specific users';

    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');
        $targets = $this->option('targets');
        $title = $this->option('title');
        $message = $this->option('message');

        if (empty($targets)) {
            $this->error('Please provide targets using --targets option');
            return 1;
        }

        if (empty($title)) {
            $this->error('Please provide a title using --title option');
            return 1;
        }

        $this->info("Sending notification...");
        $this->info("Type: {$type}");
        $this->info("Targets: " . implode(', ', $targets));
        $this->info("Title: {$title}");
        $this->info("Message: " . ($message ?: 'No message provided'));

        try {
            $result = false;

            switch ($type) {
                case 'roles':
                    $result = $this->notificationService->sendToRoles($targets, $title, $message);
                    break;
                case 'ranks':
                    $targets = array_map('intval', $targets); // Convert to integers
                    $result = $this->notificationService->sendToRanks($targets, $title, $message);
                    break;
                case 'users':
                    $targets = array_map('intval', $targets); // Convert to integers
                    $result = $this->notificationService->sendToUsers($targets, $title, $message);
                    break;
                default:
                    $this->error("Invalid type. Use: roles, ranks, or users");
                    return 1;
            }

            if ($result) {
                $this->info('✅ Notification sent successfully!');
                return 0;
            } else {
                $this->warn('⚠️  No users found for the specified targets.');
                return 0;
            }

        } catch (\Exception $e) {
            $this->error('❌ Error sending notification: ' . $e->getMessage());
            return 1;
        }
    }
}

/**
 * Usage Examples:
 *
 * Send to admin roles:
 * php artisan notification:send --type=roles --targets=admin --targets=super-admin --title="System Alert" --message="System maintenance scheduled"
 *
 * Send to specific ranks:
 * php artisan notification:send --type=ranks --targets=1 --title="Senior Staff Notice"
 *
 * Send to specific users:
 * php artisan notification:send --type=users --targets=1 --targets=5 --targets=10 --title="Personal Message" --message="You have been selected"
 */
