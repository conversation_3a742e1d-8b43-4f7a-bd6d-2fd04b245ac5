"use strict";
var KTAppMaintenanceList = (function () {
    var e,
        t,
        n,
        r,
        o,
        a = (e, n, a) => {
            let r = e[0] ? new Date(e[0]) : null;
            let o = e[1] ? new Date(e[1]) : null;

            // Remove previous filter to avoid stacking
            $.fn.dataTable.ext.search = $.fn.dataTable.ext.search.filter(fn => fn.name !== 'dateRangeFilter');

            function dateRangeFilter(settings, data, dataIndex) {
                let l = new Date(moment(data[1], "DD-MMM-YYYY"));
                return (
                    (r === null && o === null) ||
                    (r === null && o >= l) ||
                    (r <= l && o === null) ||
                    (r <= l && l <= o)
                );
            }
            $.fn.dataTable.ext.search.push(dateRangeFilter);

            t.draw();
        },
        c = () => {
        };
    return {
        init: function () {
            (e = document.querySelector("#kt_maintenance_table")) &&
                ((t = $(e).DataTable({
                    info: 1,
                    order: [],
                    pageLength: 25,
                    columnDefs: [
                        { orderable: !1, targets: 3 },
                        { orderable: !1, targets: 5 },
                        { orderable: !1, targets: 7 },
                    ],
                })).on("draw", function () {
                    c();
                }),
                (() => {
                    const e = document.querySelector("#kt_maintenance_flatpickr");
                    n = $(e).flatpickr({
                        altInput: !0,
                        altFormat: "d-M-Y",
                        dateFormat: "Y-M-d",
                        mode: "range",
                        onChange: function (e, t, n) {
                            a(e, t, n);
                        },
                    });
                })(),
                document
                    .querySelector('[data-kt-maintenance-filter="search"]')
                    .addEventListener("keyup", function (e) {
                        t.search(e.target.value).draw();
                    }),
                (() => {
                    const e = document.querySelector('[data-kt-maintenance-filter="status"]');
                    $(e).on("change", (e) => {
                        let n = e.target.value;
                        "all" === n && (n = ""), t.column(6).search(n).draw();
                    });
                })(),
                c(),
                document
                    .querySelector("#kt_maintenance_flatpickr_clear")
                    .addEventListener("click", (e) => {
                        n.clear();
                    }));
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTAppMaintenanceList.init();
});
