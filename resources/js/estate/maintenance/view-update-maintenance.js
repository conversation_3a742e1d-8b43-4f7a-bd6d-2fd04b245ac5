"use strict";
var KTUpdatesTable = (function () {
    // Define variables for table element and other utilities
    var table;

    // Function to handle row expansion/collapse
    const o = () => {
        // Get all expand row buttons
        const expandRows = table.querySelectorAll('[data-kt-updates-filter="expand_row"]');

        // Add click event listener to each expand button
        expandRows.forEach((expandRow, a) => {
            expandRow.addEventListener("click", (l) => {
                // Prevent event bubbling and default behavior
                l.stopImmediatePropagation();
                l.preventDefault();

                const s = ["isOpen", "border-bottom-0"];

                const parentRow = expandRow.closest("tr");

                const updateId = parentRow.firstElementChild.getAttribute('data-kt-update-id');
                const detailsRow = table.querySelector(`[data-kt-updates-detail-row="${updateId}"]`);

                if (parentRow.classList.contains("isOpen")) {
                    parentRow.classList.remove(...s);
                    expandRow.classList.remove("active");

                    if (detailsRow) {
                        detailsRow.classList.add('d-none');
                    }
                } else {
                    parentRow.classList.add(...s);
                    expandRow.classList.add("active");

                    if (detailsRow) {
                        detailsRow.classList.remove('d-none');
                    }
                }
            });
        });
    };

    // Return public methods
    return {
        init: function () {
            // Initialize table if it exists
            table = document.querySelector("#kt_updates_table");
            if (table) {
                o(); // Setup expand/collapse functionality
            }
        },
    };
})();
"undefined" != typeof module && (module.exports = KTUpdatesTable),
KTUtil.onDOMContentLoaded(function () {
    KTUpdatesTable.init();
});
