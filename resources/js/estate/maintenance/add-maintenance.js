"use strict";
var KTModalAddMaintenance = (function () {
    const t = document.getElementById("kt_modal_add_maintenance"),
        e = t.querySelector("#kt_modal_add_maintenance_form"),
        n = new bootstrap.Modal(t);
    return {
        init: function () {
            $(e.querySelector('[name="entry_date"]')).flatpickr({
                enableTime: false,
                dateFormat: "d-M-Y"
            });
            // FormValidation
            var o = FormValidation.formValidation(e, {
                fields: {
                    entry_date: {
                        validators: {
                            notEmpty: { message: "Entry date is required" },
                        },
                    },
                    requesting_officer_name: {
                        validators: {
                            notEmpty: { message: "Name is required" },
                        },
                    },
                    department_id: {
                        validators: {
                            notEmpty: { message: "Department is required" },
                        },
                    },
                    location: {
                        validators: {
                            notEmpty: { message: "Location is required" },
                        },
                    },
                    work_type_id: {
                        validators: {
                            notEmpty: { message: "Work type is required" },
                        },
                    },
                    other_work_type: {
                        validators: {
                            callback: {
                                message: "Other work type is required when 'Other' is selected",
                                callback: function(input) {
                                    const workTypeSelect = e.querySelector('[name="work_type_id"]');
                                    if (workTypeSelect && workTypeSelect.value) {
                                        const selectedOption = workTypeSelect.options[workTypeSelect.selectedIndex];
                                        if (selectedOption && selectedOption.text.includes('Other')) {
                                            return input.value.length > 0;
                                        }
                                    }
                                    return true;
                                }
                            }
                        },
                    },
                    description: {
                        validators: {
                            notEmpty: { message: "Description is required" },
                        },
                    },
                    proposed_action: {
                        validators: {
                            notEmpty: { message: "Action is required" },
                        },
                    },
                    cost_implication: {
                        validators: {
                            notEmpty: { message: "Cost is required" },
                        },
                    },
                    checked_by: {
                        validators: {
                            notEmpty: { message: "Checked by is required" },
                        },
                    },
                    "notifications[]": {
                        validators: {
                            notEmpty: { message: "Please select at least one notifications method" },
                        },
                    },
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: ".fv-row",
                        eleInvalidClass: "",
                        eleValidClass: "",
                    }),
                },
            });

            // Modal close
            t.querySelector('[data-kt-maintenance-modal-action="close"]').addEventListener("click", (evt) => {
                evt.preventDefault();
                Swal.fire({
                    text: "Are you sure you would like to close?",
                    icon: "warning",
                    showCancelButton: !0,
                    buttonsStyling: !1,
                    confirmButtonText: "Yes, close it!",
                    cancelButtonText: "No, return",
                    customClass: {
                        confirmButton: "btn btn-primary",
                        cancelButton: "btn btn-active-light",
                    },
                }).then(function (result) {
                    result.value && (e.reset(), n.hide());
                });
            });
            // Modal cancel
            t.querySelector('[data-kt-maintenance-modal-action="cancel"]').addEventListener("click", (evt) => {
                evt.preventDefault();
                Swal.fire({
                    text: "Are you sure you would like to cancel?",
                    icon: "warning",
                    showCancelButton: !0,
                    buttonsStyling: !1,
                    confirmButtonText: "Yes, cancel it!",
                    cancelButtonText: "No, return",
                    customClass: {
                        confirmButton: "btn btn-primary",
                        cancelButton: "btn btn-active-light",
                    },
                }).then(function (result) {
                    result.value
                        ? (e.reset(), n.hide())
                        : "cancel" === result.dismiss &&
                        Swal.fire({
                            text: "Your form has not been cancelled!.",
                            icon: "error",
                            buttonsStyling: !1,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-primary",
                            },
                        });
                });
            });

            // --- Multiple file preview and removal logic ---
            var fileInput = e.querySelector('input[name="supporting_doc_url[]"]');
            var previewContainer = document.getElementById('supporting-docs-preview');
            var selectedFiles = [];
            if (fileInput) {
                fileInput.addEventListener('change', function(ev) {
                    for (var i = 0; i < ev.target.files.length; i++) {
                        selectedFiles.push(ev.target.files[i]);
                    }
                    renderPreviews();
                    fileInput.value = '';
                });
            }
            function renderPreviews() {
                if (!previewContainer) return;
                previewContainer.innerHTML = '';
                selectedFiles.forEach(function(file, idx) {
                    var url = URL.createObjectURL(file);
                    var wrapper = document.createElement('div');
                    wrapper.className = 'position-relative m-1 shadow-sm bg-white rounded d-flex flex-column align-items-center justify-content-center';
                    wrapper.style.width = '90px';
                    wrapper.style.minHeight = '90px';
                    wrapper.style.border = '1px solid #e5e5e5';
                    wrapper.style.overflow = 'hidden';
                    wrapper.style.flex = '0 0 auto';
                    var content;
                    if (file.type.indexOf('image/') === 0) {
                        content = '<img src="' + url + '" style="width:88px;height:88px;object-fit:cover;border-radius:6px;" />';
                    } else {
                        content = '<div class="d-flex flex-column align-items-center justify-content-center" style="width:88px;height:88px;">' +
                            '<i class="ki-outline ki-file fs-2"></i>' +
                            '<span class="fs-8">' + file.name.split('.').pop().toUpperCase() + '</span>' +
                            '</div>';
                    }
                    wrapper.innerHTML =
                        content +
                        '<button type="button" class="btn btn-xs btn-danger position-absolute top-0 end-0 m-1 px-1 py-0" style="border-radius:50%;width:22px;height:22px;line-height:1;z-index:2;" data-remove-idx="' + idx + '">' +
                        '<i class="ki-outline ki-cross fs-6"></i>' +
                        '</button>';
                    previewContainer.appendChild(wrapper);
                });
                previewContainer.style.display = 'flex';
                previewContainer.style.flexWrap = 'wrap';
                previewContainer.style.gap = '12px';
            }
            if (previewContainer) {
                previewContainer.addEventListener('click', function(e) {
                    var btn = e.target.closest('button[data-remove-idx]');
                    if (btn) {
                        var idx = parseInt(btn.getAttribute('data-remove-idx'));
                        selectedFiles.splice(idx, 1);
                        renderPreviews();
                    }
                });
            }

            // Submit button
            const i = t.querySelector('[data-kt-maintenance-modal-action="submit"]');
            i.addEventListener("click", (evt) => {
                evt.preventDefault();
                o && o.validate().then(function (result) {
                    if ("Valid" == result) {
                        i.setAttribute("data-kt-indicator", "on");
                        i.disabled = !0;
                        // Use FormData for file upload
                        const formData = new FormData(e);
                        // Remove any existing files in FormData
                        formData.delete('supporting_doc_url[]');
                        selectedFiles.forEach(function(file) {
                            formData.append('supporting_doc_url[]', file);
                        });
                        axios
                            .post("/estate/maintenance/internal", formData, {
                                headers: {
                                    "X-CSRF-TOKEN":
                                        document.querySelector(
                                            "[name=_token]"
                                        ).value,
                                    "Content-Type": "multipart/form-data"
                                },
                            })
                            .then(function (response) {
                                i.removeAttribute("data-kt-indicator");
                                i.disabled = !1;
                                Swal.fire({
                                    text: "Entry has been successfully added!",
                                    icon: "success",
                                    buttonsStyling: !1,
                                    confirmButtonText:
                                        "Ok, got it!",
                                    customClass: {
                                        confirmButton:
                                            "btn btn-primary",
                                    },
                                }).then(function (t) {
                                    t.isConfirmed && n.hide();
                                    window.location.reload();
                                });
                            })
                            .catch(function (error) {
                                i.removeAttribute("data-kt-indicator");
                                i.disabled = !1;
                                if (
                                    error.response &&
                                    error.response.status === 422
                                ) {
                                    let errors = error.response.data.errors;
                                    let errorList = "<ul>";
                                    for (let key in errors) {
                                        if (errors.hasOwnProperty(key)) {
                                            errors[key].forEach((msg) => {
                                                errorList += `<li>${msg}</li>`;
                                            });
                                        }
                                    }
                                    errorList += "</ul>";
                                    Swal.fire({
                                        html: errorList,
                                        icon: "error",
                                        buttonsStyling: !1,
                                        confirmButtonText:
                                            "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    });
                                }
                            });
                    } else {
                        Swal.fire({
                            text: "Sorry, looks like there are some errors detected, please try again.",
                            icon: "error",
                            buttonsStyling: !1,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-primary",
                            },
                        });
                    }
                });
            });
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTModalAddMaintenance.init();
});
