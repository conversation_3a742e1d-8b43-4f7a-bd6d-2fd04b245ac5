document.addEventListener('DOMContentLoaded', function () {
    const checkboxes = document.querySelectorAll('.notification-checkbox');
    const markSelectedBtn = document.getElementById('kt_widget_5_load_more_btn');
    const selectedCount = document.getElementById('selected-count');

    function updateSelectedCount() {
        const count = Array.from(checkboxes).filter(cb => cb.checked).length;
        if (selectedCount) {
            selectedCount.textContent = `${count} selected`;
        }
    }

    checkboxes.forEach(function (checkbox) {
        checkbox.addEventListener('change', updateSelectedCount);
    });
    updateSelectedCount();

    if (!markSelectedBtn) return;

    markSelectedBtn.addEventListener('click', function (e) {
        e.preventDefault();
        const selected = Array.from(checkboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.getAttribute('data-notification-id'));
        if (selected.length === 0) {
            Swal.fire({
                text: 'Please select at least one notification.',
                icon: 'warning',
                confirmButtonText: 'Ok',
                customClass: { confirmButton: 'btn btn-primary' },
                buttonsStyling: false
            });
            return;
        }
        Swal.fire({
            text: `Do you want to mark ${selected.length} notification(s) as read?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, mark as read',
            cancelButtonText: 'No',
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-active-light',
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                markSelectedBtn.classList.add('disabled');
                markSelectedBtn.querySelector('.indicator-label').style.display = 'none';
                markSelectedBtn.querySelector('.indicator-progress').style.display = 'inline-block';
                axios.post('/notifications/mark-as-read-bulk', { ids: selected })
                    .then(function (response) {
                        if (response.data.success) {
                            Swal.fire({
                                text: 'Selected notifications marked as read!',
                                icon: 'success',
                                confirmButtonText: 'Ok',
                                customClass: { confirmButton: 'btn btn-primary' },
                                buttonsStyling: false
                            }).then(function () {
                                window.location.reload();
                            });
                        } else {
                            Swal.fire({
                                text: response.data.message || 'Failed to mark as read.',
                                icon: 'error',
                                confirmButtonText: 'Ok',
                                customClass: { confirmButton: 'btn btn-primary' },
                                buttonsStyling: false
                            });
                        }
                    })
                    .catch(function () {
                        Swal.fire({
                            text: 'Failed to mark as read.',
                            icon: 'error',
                            confirmButtonText: 'Ok',
                            customClass: { confirmButton: 'btn btn-primary' },
                            buttonsStyling: false
                        });
                    })
                    .finally(function () {
                        markSelectedBtn.classList.remove('disabled');
                        markSelectedBtn.querySelector('.indicator-label').style.display = 'inline-block';
                        markSelectedBtn.querySelector('.indicator-progress').style.display = 'none';
                    });
            }
        });
    });
});
