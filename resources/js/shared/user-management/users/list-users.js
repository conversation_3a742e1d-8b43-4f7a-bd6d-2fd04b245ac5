"use strict";
var KTUsersList = (function () {
    var e,
        t,
        n,
        r,
        o = document.getElementById("kt_users_view_table"),
        l = () => {
            const c = o.querySelectorAll('[type="checkbox"]');
            (t = document.querySelector('[data-kt-user-table-toolbar="base"]')),
                (n = document.querySelector(
                    '[data-kt-user-table-toolbar="selected"]'
                )),
                (r = document.querySelector(
                    '[data-kt-user-table-select="selected_count"]'
                ));
            const s = document.querySelector(
                '[data-kt-user-table-select="toggle_selected"]'
            );
            c.forEach((e) => {
                e.addEventListener("click", function () {
                    setTimeout(function () {
                        a();
                    }, 50);
                });
            }),
                s.addEventListener("click", function () {
                    Swal.fire({
                        text: "Are you sure you want to toggle the status selected users?",
                        icon: "warning",
                        showCancelButton: !0,
                        buttonsStyling: !1,
                        confirmButtonText: "Yes, proceed!",
                        cancelButtonText: "No, cancel",
                        customClass: {
                            confirmButton: "btn fw-bold btn-danger",
                            cancelButton:
                                "btn fw-bold btn-active-light-primary",
                        },
                    }).then(function (t) {
                        t.value
                            ? Swal.fire({
                                text: "You have switched the status of all selected users!.",
                                icon: "success",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn fw-bold btn-primary",
                                },
                            })
                            .then(function () {
                                c.forEach((t) => {
                                    t.checked &&
                                        e.row($(t.closest("tbody tr")))
                                            .remove()
                                            .draw();
                                });
                                o.querySelectorAll(
                                    '[type="checkbox"]'
                                )[0].checked = !1;
                            })
                            .then(function () {
                                a(), l();
                            })
                        : "cancel" === t.dismiss &&
                            Swal.fire({
                                text: "Selected users status was not toggled.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn fw-bold btn-primary",
                                },
                            });
                    });
                });
        };
    const a = () => {
        const e = o.querySelectorAll('tbody [type="checkbox"]');
        let c = !1,
            l = 0;
        e.forEach((e) => {
            e.checked && ((c = !0), l++);
        }),
            c
                ? ((r.innerHTML = l),
                  t.classList.add("d-none"),
                  n.classList.remove("d-none"))
                : (t.classList.remove("d-none"), n.classList.add("d-none"));
    };
    return {
        init: function () {
            o &&
                (o.querySelectorAll("tbody tr").forEach((e) => {
                    const t = e.querySelectorAll("td");
                    // const l = moment(t[4].innerHTML, "DD MMM YYYY, LT").format();
                    // t[4].setAttribute("data-order", l);
                }),
                (e = $(o).DataTable({
                    info: !1,
                    order: [],
                    pageLength: 25,
                    columnDefs: [
                        { orderable: !1, targets: 1 },
                        { orderable: !1, targets: 6 },
                    ],
                })).on("draw", function () {
                    l(), a();
                }),
                l(),
                document
                    .querySelector('[data-kt-user-table-filter="search"]')
                    .addEventListener("keyup", function (t) {
                        e.search(t.target.value).draw();
                    }),
                document
                    .querySelector('[data-kt-user-table-filter="reset"]')
                    .addEventListener("click", function () {
                        document
                            .querySelector('[data-kt-user-table-filter="form"]')
                            .querySelectorAll("select")
                            .forEach((e) => {
                                $(e).val("").trigger("change");
                            }),
                            e.search("").draw();
                    }),
                // This is an Immediately Invoked Function Expression (IIFE) that handles filtering
                (() => {
                    // Get the filter form and its elements
                    const t = document.querySelector('[data-kt-user-table-filter="form"]'), // The form element
                        n = t.querySelector('[data-kt-user-table-filter="filter"]'),        // The filter button
                        r = t.querySelectorAll("select");                                   // All select elements in form

                    // Add click event listener to the filter button
                    n.addEventListener("click", function () {
                        var t = "";
                        // Loop through all select elements
                        r.forEach((e, n) => {
                            // If select has a value, add it to filter string
                            if (e.value && e.value !== "") {
                                // Add space between values except for first value
                                if (n !== 0) t += " ";
                                t += e.value;
                            }
                        }),
                        // Apply the filter to DataTable and redraw
                        console.log(t);

                        e.search(t).draw();
                    });
                })());
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUsersList.init();
});
