"use strict";
var KTUsersAddUser = (function () {
    const t = document.getElementById("kt_modal_add_user"),
        e = t.querySelector("#kt_modal_add_user_form"),
        n = new bootstrap.Modal(t);
    return {
        init: function () {
            (() => {
                var o = FormValidation.formValidation(e, {
                    fields: {
                        // avatar: {
                        //     validators: {
                        //         file: {
                        //             extension: "png,jpg,jpeg",
                        //             type: "image/jpeg,image/png",
                        //             message: "Please choose a png, jpg or jpeg files only",
                        //         },
                        //     },
                        // },
                        identifier: {
                            validators: {
                                notEmpty: { message: "ID is required" },
                            },
                        },
                        fullname: {
                            validators: {
                                notEmpty: { message: "Full name is required" },
                            },
                        },
                        email: {
                            validators: {
                                notEmpty: { message: "Email is required" },
                                emailAddress: {
                                    message:
                                        "The value is not a valid email address",
                                },
                            },
                        },
                        phone_no: {
                            validators: {
                                notEmpty: {
                                    message: "Phone number is required",
                                },
                            },
                        },
                        password: {
                            validators: {
                                notEmpty: { message: "Password is required" },
                                stringLength: {
                                    min: 8,
                                    message:
                                        "Password must be at least 8 characters",
                                },
                            },
                        },
                        password_confirmation: {
                            validators: {
                                notEmpty: {
                                    message:
                                        "Password confirmation is required",
                                },
                                identical: {
                                    compare: function () {
                                        return e.querySelector(
                                            '[name="password"]'
                                        ).value;
                                    },
                                    message:
                                        "The password and its confirmation do not match",
                                },
                            },
                        },
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: "",
                        }),
                    },
                });
                t.querySelector('[data-kt-user-modal-action="close"]')
                        .addEventListener("click", (t) => {
                            t.preventDefault(),
                                Swal.fire({
                                    text: "Are you sure you would like to close?",
                                    icon: "warning",
                                    showCancelButton: !0,
                                    buttonsStyling: !1,
                                    confirmButtonText: "Yes, close it!",
                                    cancelButtonText: "No, return",
                                    customClass: {
                                        confirmButton: "btn btn-primary",
                                        cancelButton: "btn btn-active-light",
                                    },
                                }).then(function (t) {
                                    t.value && (e.reset(), n.hide());
                                });
                        });
                t.querySelector('[data-kt-user-modal-action="cancel"]')
                    .addEventListener("click", (t) => {
                        t.preventDefault(),
                            Swal.fire({
                                text: "Are you sure you would like to cancel?",
                                icon: "warning",
                                showCancelButton: !0,
                                buttonsStyling: !1,
                                confirmButtonText: "Yes, cancel it!",
                                cancelButtonText: "No, return",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                    cancelButton: "btn btn-active-light",
                                },
                            }).then(function (t) {
                                t.value
                                    ? (e.reset(), n.hide())
                                    : "cancel" === t.dismiss &&
                                    Swal.fire({
                                        text: "Your form has not been cancelled!.",
                                        icon: "error",
                                        buttonsStyling: !1,
                                        confirmButtonText: "Ok, got it!",
                                        customClass: {
                                            confirmButton: "btn btn-primary",
                                        },
                                    });
                            });
                    });
                const i = t.querySelector('[data-kt-user-modal-action="submit"]');
                i.addEventListener("click", (t) => {
                    t.preventDefault();
                    o && o.validate().then(function (t) {
                        if ("Valid" == t) {
                            i.setAttribute("data-kt-indicator", "on"),
                            (i.disabled = !0);
                            // Use FormData for file upload
                            const formData = new FormData(e);
                            axios
                                .post("/users", formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                        "Content-Type": "multipart/form-data"
                                    },
                                })
                                .then(function (response) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    Swal.fire({
                                        text: "User has been successfully added!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText:
                                            "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    }).then(function (t) {
                                        t.isConfirmed && n.hide();
                                        window.location.reload();
                                    });
                                })
                                .catch(function (error) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors =
                                            error.response.data.errors;
                                        let errorList = "<ul>";
                                        for (let key in errors) {
                                            if (errors.hasOwnProperty(key)) {
                                                errors[key].forEach((msg) => {
                                                    errorList += `<li>${msg}</li>`;
                                                });
                                            }
                                        }
                                        errorList += "</ul>";
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        } else {
                            Swal.fire({
                                text: "Sorry, looks like there are some errors detected, please try again.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                },
                            });
                        }
                    });
                });
            })();
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUsersAddUser.init();
});
