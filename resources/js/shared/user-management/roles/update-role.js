"use strict";
var KTUsersUpdateRole = (function () {
    const t = document.getElementById("kt_update_role"),
        e = t.querySelector("#kt_update_role_form");
    return {
        init: function () {
            (() => {
                var o = FormValidation.formValidation(e, {
                    fields: {
                        name: {
                            validators: {
                                notEmpty: { message: "Role name is required" },
                            },
                        },
                        slug: {
                            validators: {
                                notEmpty: { message: "Role slug is required" },
                            },
                        },
                        description: {
                            validators: {
                                notEmpty: { message: "Role description is required" },
                            },
                        },
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: "",
                        }),
                    },
                });
                const i = t.querySelector('[data-kt-roles-action="submit"]');
                i.addEventListener("click", function (t) {
                    t.preventDefault();
                    o && o.validate().then(function (t) {
                        if ("Valid" == t) {
                            i.setAttribute("data-kt-indicator", "on");
                            i.disabled = !0;
                            const formData = {};
                            const elements = e.querySelectorAll("[name]");
                            formData.permissions = [];
                            elements.forEach((el) => {
                                if (el.type === "checkbox" && el.name === "permissions[]") {
                                    if (el.checked) {
                                        formData.permissions.push(el.value);
                                    }
                                } else if (el.name && el.type !== "checkbox") {
                                    formData[el.name] = el.value;
                                }
                            });
                            const role = document.querySelector("[name=_role]").value;
                            axios
                                .put(`/roles/${role}`, formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                    },
                                })
                                .then(function (response) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    Swal.fire({
                                        text: "Form has been successfully submitted!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText:
                                            "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    }).then(function (t) {
                                        window.location.href = "/roles";
                                    });
                                })
                                .catch(function (error) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors =
                                            error.response.data.errors;
                                        let errorList = "<ul>";
                                        for (let key in errors) {
                                            if (
                                                errors.hasOwnProperty(
                                                    key
                                                )
                                            ) {
                                                errors[key].forEach(
                                                    (msg) => {
                                                        errorList += `<li>${msg}</li>`;
                                                    }
                                                );
                                            }
                                        }
                                        errorList += "</ul>";
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        } else {
                            Swal.fire({
                                text: "Sorry, looks like there are some errors detected, please try again.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton:
                                        "btn btn-primary",
                                },
                            });
                        }
                    });
                });
                (() => {
                    const t = e.querySelector("#kt_roles_select_all");
                    const n = e.querySelectorAll('[type="checkbox"]');
                    t.addEventListener("change", (t) => {
                        n.forEach((e) => {
                            e.checked = t.target.checked;
                        });
                    });
                })();
            })();
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUsersUpdateRole.init();
});
