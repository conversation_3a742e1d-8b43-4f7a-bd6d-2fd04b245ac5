"use strict";
var KTUsersViewRole = (function () {
    var t,
        e,
        o = () => {
            const r = e.querySelectorAll('[type="checkbox"]'),
                c = document.querySelector(
                    '[data-kt-view-roles-table-select="unassign_selected"]'
                );
            r.forEach((t) => {
                t.addEventListener("click", function () {
                    setTimeout(function () {
                        n();
                    }, 50);
                });
            }),
                c.addEventListener("click", function () {
                    Swal.fire({
                        text: "Are you sure you want to unassign the selected users from this role?",
                        icon: "warning",
                        showCancelButton: !0,
                        buttonsStyling: !1,
                        confirmButtonText: "Yes, unassign!",
                        cancelButtonText: "No, cancel",
                        customClass: {
                            confirmButton: "btn fw-bold btn-danger",
                            cancelButton:
                                "btn fw-bold btn-active-light-primary",
                        },
                    }).then(function (t) {
                        if ("cancel" === t.dismiss) {
                            Swal.fire({
                                text: "Action was cancelled!.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                },
                            });
                        } else {
                            c.setAttribute("data-kt-indicator", "on");
                            c.disabled = !0;
                            const formData = {};
                            const elements = e.querySelectorAll("[name]");
                            formData.users = [];
                            elements.forEach((el) => {
                                if (
                                    el.type === "checkbox" &&
                                    el.name === "users[]"
                                ) {
                                    if (el.checked) {
                                        formData.users.push(el.value);
                                    }
                                } else if (el.name && el.type !== "checkbox") {
                                    formData[el.name] = el.value;
                                }
                            });
                            const role = e.querySelector("[name=_role]").value;
                            axios
                                .put(`/roles/${role}/unassign-many`, formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                    },
                                })
                                .then(function (response) {
                                    c.removeAttribute("data-kt-indicator");
                                    c.disabled = !1;
                                    Swal.fire({
                                        text: "You have unassigned the selected users from this role!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText: "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn fw-bold btn-primary",
                                        },
                                    }).then(function (t) {
                                        t.value && (window.location.href = `/roles/${role}`);
                                    });
                                })
                                .catch(function (error) {
                                    c.removeAttribute("data-kt-indicator");
                                    c.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors = error.response.data.errors;
                                        let errorList = "<ul>";
                                        for (let key in errors) {
                                            if (errors.hasOwnProperty(key)) {
                                                errors[key].forEach((msg) => {
                                                    errorList += `<li>${msg}</li>`;
                                                });
                                            }
                                        }
                                        errorList += "</ul>";
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText: "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        }
                    });
                });
        };
    const n = () => {
        const t = document.querySelector(
                '[data-kt-view-roles-table-toolbar="base"]'
            ),
            o = document.querySelector(
                '[data-kt-view-roles-table-toolbar="selected"]'
            ),
            n = document.querySelector(
                '[data-kt-view-roles-table-select="selected_count"]'
            ),
            r = e.querySelectorAll('tbody [type="checkbox"]');
        let c = !1,
            l = 0;
        r.forEach((t) => {
            t.checked && ((c = !0), l++);
        }),
            c
                ? ((n.innerHTML = l),
                    t.classList.add("d-none"),
                    o.classList.remove("d-none"))
                : (t.classList.remove("d-none"), o.classList.add("d-none"));
    };
    return {
        init: function () {
            (e = document.querySelector("#kt_roles_view_table")) &&
                (e.querySelectorAll("tbody tr").forEach((t) => {
                    const e = t.querySelectorAll("td"),
                        o = moment(e[3].innerHTML, "DD MMM YYYY, LT").format();
                    e[3].setAttribute("data-order", o);
                }),
                (t = $(e).DataTable({
                    info: !1,
                    order: [],
                    pageLength: 5,
                    lengthChange: !1,
                    columnDefs: [
                        { orderable: !1, targets: 0 },
                        { orderable: !1, targets: 4 },
                    ],
                })),
                document
                    .querySelector('[data-kt-roles-table-filter="search"]')
                    .addEventListener("keyup", function (e) {
                        t.search(e.target.value).draw();
                    }),
                o());
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUsersViewRole.init();
});
