"use strict";
var KTUsersUpdatePermission = (function () {
    const t = document.getElementById("kt_update_permission");
    const e = t.querySelector("#kt_update_permission_form");
    return {
        init: function () {
            (() => {
                var o = FormValidation.formValidation(e, {
                    fields: {
                        sub_module_id: {
                            validators: {
                                notEmpty: {
                                    message: "Sub-module is required",
                                },
                            },
                        },
                        name: {
                            validators: {
                                notEmpty: {
                                    message: "Permission name is required",
                                },
                            },
                        },
                        action: {
                            validators: {
                                notEmpty: {
                                    message: "Action\\Verb is required",
                                },
                            },
                        },
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: "",
                        }),
                    },
                });
                const i = t.querySelector('[data-kt-permissions-action="submit"]');
                i.addEventListener("click", function (t) {
                    t.preventDefault();
                    o && o.validate().then(function (t) {
                        if ("Valid" == t) {
                            i.setAttribute("data-kt-indicator", "on");
                            i.disabled = !0;
                            const formData = {};
                            const elements = e.querySelectorAll("[name]");
                            elements.forEach((el) => {
                                if (el.name) {
                                    formData[el.name] = el.value;
                                }
                            });
                            const permission = document.querySelector("[name=_permission]").value;
                            axios
                                .put(`/permissions/${permission}`, formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                    },
                                })
                                .then(function (response) {
                                    i.removeAttribute(
                                        "data-kt-indicator"
                                    );
                                    i.disabled = !1;
                                    Swal.fire({
                                        text: "Form has been successfully submitted!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText:
                                            "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    }).then(function (t) {
                                        window.location.href = "/permissions";
                                    });
                                })
                                .catch(function (error) {
                                    i.removeAttribute(
                                        "data-kt-indicator"
                                    );
                                    i.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors =
                                            error.response.data.errors;
                                        let errorList = "<ul>";
                                        for (let key in errors) {
                                            if (
                                                errors.hasOwnProperty(
                                                    key
                                                )
                                            ) {
                                                errors[key].forEach(
                                                    (msg) => {
                                                        errorList += `<li>${msg}</li>`;
                                                    }
                                                );
                                            }
                                        }
                                        errorList += "</ul>";
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        } else {
                            Swal.fire({
                                text: "Sorry, looks like there are some errors detected, please try again.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                },
                            });
                        }
                    });
                });
            })();
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUsersUpdatePermission.init();
});
