"use strict";
var KTUsersAddPermission = (function () {
    const t = document.getElementById("kt_modal_add_permission"),
        e = t.querySelector("#kt_modal_add_permission_form"),
        n = new bootstrap.Modal(t);
    return {
        init: function () {
            (() => {
                var o = FormValidation.formValidation(e, {
                    fields: {
                        sub_module_id: {
                            validators: {
                                notEmpty: {
                                    message: "Module is required",
                                },
                            },
                        },
                        name: {
                            validators: {
                                notEmpty: {
                                    message: "Permission name is required",
                                },
                            },
                        },
                        action: {
                            validators: {
                                notEmpty: {
                                    message: "Action\\Verb is required",
                                },
                            },
                        },
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: "",
                        }),
                    },
                });
                t
                    .querySelector('[data-kt-permissions-modal-action="close"]')
                    .addEventListener("click", (t) => {
                        t.preventDefault();
                        Swal.fire({
                            text: "Are you sure you would like to close?",
                            icon: "warning",
                            showCancelButton: !0,
                            buttonsStyling: !1,
                            confirmButtonText: "Yes, close it!",
                            cancelButtonText: "No, return",
                            customClass: {
                                confirmButton: "btn btn-primary",
                                cancelButton: "btn btn-active-light",
                            },
                        }).then(function (t) {
                            t.value && n.hide();
                        });
                    }),
                    t
                        .querySelector(
                            '[data-kt-permissions-modal-action="cancel"]'
                        )
                        .addEventListener("click", (t) => {
                            t.preventDefault();
                            Swal.fire({
                                text: "Are you sure you would like to cancel?",
                                icon: "warning",
                                showCancelButton: !0,
                                buttonsStyling: !1,
                                confirmButtonText: "Yes, cancel it!",
                                cancelButtonText: "No, return",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                    cancelButton: "btn btn-active-light",
                                },
                            }).then(function (t) {
                                t.value && (e.reset(), n.hide());
                            });
                        });
                const i = t.querySelector(
                    '[data-kt-permissions-modal-action="submit"]'
                );
                i.addEventListener("click", function (t) {
                    t.preventDefault();
                    o && o.validate().then(function (t) {
                        if ("Valid" == t) {
                            i.setAttribute("data-kt-indicator", "on");
                            i.disabled = !0;
                            const formData = {};
                            const elements = e.querySelectorAll("[name]");
                            elements.forEach((el) => {
                                if (el.name) {
                                    formData[el.name] = el.value;
                                }
                            });
                            axios
                                .post("/permissions", formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                    },
                                })
                                .then(function (response) {
                                    i.removeAttribute(
                                        "data-kt-indicator"
                                    );
                                    i.disabled = !1;
                                    Swal.fire({
                                        text: "Form has been successfully submitted!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText: "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    }).then(function (t) {
                                        t.isConfirmed && n.hide();
                                        window.location.reload();
                                    });
                                })
                                .catch(function (error) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors = error.response.data.errors;
                                        let errorList = '<ul>';
                                        for (let key in errors) {
                                            if (errors.hasOwnProperty(key)) {
                                                errors[key].forEach(msg => {
                                                    errorList += `<li>${msg}</li>`;
                                                });
                                            }
                                        }
                                        errorList += '</ul>';
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        } else {
                            Swal.fire({
                                text: "Sorry, looks like there are some errors detected, please try again.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                },
                            });
                        }
                    });
                });
            })();
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUsersAddPermission.init();
});
