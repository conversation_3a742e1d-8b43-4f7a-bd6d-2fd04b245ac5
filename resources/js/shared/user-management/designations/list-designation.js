"use strict";
var KTUserDesignationList = (function () {
    var e,
        t,
        n,
        c = () => {
        };
    return {
        init: function () {
            (e = document.querySelector("#kt_designation_table")) &&
                ((t = $(e).DataTable({
                    info: 1,
                    order: [],
                    pageLength: 25,
                    columnDefs: [
                        { orderable: !1, targets: 4 },
                    ],
                    })).on("draw", function () {
                        c();
                    }),
                    document
                        .querySelector('[data-kt-designation-filter="search"]')
                        .addEventListener("keyup", function (e) {
                            t.search(e.target.value).draw();
                        }),
                    (() => {
                        const e = document.querySelector('[data-kt-designation-filter="status"]');
                        $(e).on("change", (e) => {
                            let n = e.target.value;
                            "all" === n && (n = ""), t.column(3).search(n).draw();
                        });
                    })(),
                    c()
                );
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUserDesignationList.init();
});
