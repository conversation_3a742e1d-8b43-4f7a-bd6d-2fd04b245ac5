@props(['maintenances', 'departments', 'workTypes', 'users', 'workflow'])
@php
    $breadCrumbs = [
        ['name' => __('Home'), 'url' => route('dashboard')],
        ['name' => __('Estate')],
        ['name' => __('Maintenance')],
        ['name' => __('Internal Maintenance List'), 'url' => route('estate.maintenance.internal.index')],
    ];
    // dd($maintenances, $departments, $workTypes, $users, $workflow);
@endphp

<x-app-layout :title="__('Estate Maintenance (Internal)')" :breadCrumbs="$breadCrumbs">
    @push('header')
        <link href="{{ asset('assets/plugins/custom/datatables/datatables.bundle.css') }}" rel="stylesheet" type="text/css">
    @endpush

    <div class="d-flex flex-wrap flex-stack mb-8">
        <!--begin::Wrapper-->
        <div class="d-flex flex-column flex-grow-1 pe-8">
            <!--begin::Stats-->
            <div class="d-flex flex-wrap">
                <livewire:statistic-info-card
                    icon="ki-outline ki-arrow-right"
                    title="Total Internal Maintenance"
                    color="primary"
                    type="internal-maintenance-all"
                />

                <livewire:statistic-info-card
                    icon="ki-outline ki-arrow-right"
                    title="Active Internal Maintenance"
                    color="danger"
                    type="internal-maintenance-active"
                />

                <livewire:statistic-info-card
                    icon="ki-outline ki-arrow-right"
                    title="Completed Internal Maintenance"
                    color="success"
                    type="internal-maintenance-percentage-completed"
                />

                <livewire:statistic-info-card
                    icon="ki-outline ki-arrow-right"
                    title="Total Internal Maintenance Cost (this year)"
                    color="primary"
                    type="internal-maintenance-total-cost"
                />
            </div>
            <!--end::Stats-->
        </div>
        <!--end::Wrapper-->
    </div>

    <!--begin::Products-->
    <div class="card card-flush border border-gray-300">
        <!--begin::Card header-->
        <div class="card-header align-items-center py-5 gap-2 gap-md-5">
            <!--begin::Card title-->
            <div class="card-title">
                <!--begin::Search-->
                <div class="d-flex align-items-center position-relative my-1">
                    <i class="ki-outline ki-magnifier fs-3 position-absolute ms-4"></i>
                    <input type="text" data-kt-maintenance-filter="search"
                        class="form-control form-control-solid w-250px ps-12" placeholder="Search Maintenance"/>
                </div>
                <!--end::Search-->
            </div>
            <!--end::Card title-->
            <!--begin::Card toolbar-->
            <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
                <!--begin::Flatpickr-->
                <div class="input-group w-250px">
                    <input class="form-control form-control-solid rounded rounded-end-0"
                        placeholder="Pick date range" id="kt_maintenance_flatpickr"/>
                    <button class="btn btn-icon btn-light" id="kt_maintenance_flatpickr_clear">
                        <i class="ki-outline ki-cross fs-2"></i>
                    </button>
                </div>
                <!--end::Flatpickr-->
                <div class="w-100 mw-150px">
                    <!--begin::Select2-->
                    <select class="form-select form-select-solid" data-control="select2" data-hide-search="true"
                        data-placeholder="Status" data-kt-maintenance-filter="status">
                        <option></option>
                        <option value="all">All</option>
                        <option value="completed">Completed</option>
                        <option value="refunded">Refunded</option>
                        <option value="failed">Failed</option>
                        <option value="processing">Processing</option>
                        <option value="pending">Pending</option>
                    </select>
                    <!--end::Select2-->
                </div>

                <!--begin::Add Maintenance-->
                @include('estate.estate.maintenance.internal-maintenance.modal__add_maintenance', [
                    'departments' => $departments,
                    'workTypes' => $workTypes,
                    'users' => $users,
                    'workflow' => $workflow
                ])
                <!--end::Add Maintenance-->
            </div>
            <!--end::Card toolbar-->
        </div>
        <!--end::Card header-->
        <!--begin::Card body-->
        <div class="card-body pt-0">
            <!--begin::Table-->
            <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_maintenance_table">
                <thead>
                <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                    <th class="min-w-20px text-start">Entry_ID</th>
                    <th class="min-w-50px text-start">Entry Date</th>
                    <th class="min-w-100px text-start">Department</th>
                    <th class="min-w-50px text-start">Location</th>
                    <th class="min-w-100px text-start">Work Type</th>
                    <th class="min-w-50px text-start">Cost</th>
                    <th class=" min-w-70px text-start">Status</th>
                    <th class="min-w-50px text-end">Actions</th>
                </tr>
                </thead>
                <tbody class="fw-semibold text-gray-600">
                @foreach($maintenances as $maintenance)
                    <tr>
                        <td class="text-start" data-kt-maintenance-filter="maintenance_id">
                                <span class="text-gray-800 text-hover-primary fw-bold">
                                    {{ $maintenance->id }}
                                </span>
                        </td>
                        <td class="text-start" data-maintenance="{{ $maintenance->entry_date->format('d-M-Y') }}">
                            <span class="fw-bold">{{ $maintenance->entry_date->format('d-M-Y') }}</span>
                        </td>
                        <td class="text-start">
                            <span class="fw-bold">{{ $maintenance->department->name }}</span>
                        </td>
                        <td class="text-start">
                            <span class="fw-bold fs-7">{{ $maintenance->location }}</span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <!--begin:: Avatar -->
                                <div class="symbol symbol-40px me-3">
                                        <span
                                            class="symbol-label fs-3 bg-light-{{ $maintenance->workType->color }} text-{{ $maintenance->workType->color }}">
                                            {{ \Illuminate\Support\Str::substr($maintenance->workType->name, 0, 1) }}
                                        </span>
                                </div>
                                <!--end::Avatar-->
                                <div class="ms-5">
                                    <!--begin::Title-->
                                    <span class="text-gray-800 text-hover-primary fs-5 fw-bold">
                                            {{ $maintenance->workType->name }}
                                        </span>
                                    <!--end::Title-->
                                </div>
                            </div>
                        </td>
                        <td class="text-start">
                            <span class="fw-bold">₦ {{ number_format($maintenance->cost_implication, 2) }}</span>
                        </td>
                        <td class="text-start" data-maintenance="{{ $maintenance->status }}">
                            <!--begin::Badges-->
                            @php
                                $statusColor = match ($maintenance->status) {
                                    'completed' => 'success',
                                    'refunded' => 'dark',
                                    'failed' => 'danger',
                                    'processing' => 'warning',
                                    'pending' => 'info', // 'secondary',
                                    default => 'info',
                                };
                            @endphp
                            <div class="text-uppercase badge badge-outline badge-{{ $statusColor }}">{{ $maintenance->status }}</div>
                            <!--end::Badges-->
                        </td>
                        <td class="text-end">
                            <a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary"
                                data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
                                <i class="ki-outline ki-down fs-5 ms-1"></i></a>
                            <!--begin::Menu-->
                            <div
                                class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                                data-kt-menu="true">

                                <a href="{{ route('estate.maintenance.internal.show', [$maintenance->id]) }}"
                                    class="btn btn-sm btn-primary hover-rotate-end m-2">
                                    <i class="ki-outline ki-eye fs-4"></i>
                                    Manage
                                    <span class="position-absolute top-0 start-100 translate-middle badge badge-square badge-danger">{{ $maintenance->workflow->stage_number }}</span>
                                </a>
                                <a href="{{ route('estate.maintenance.internal.edit', [$maintenance->id]) }}"
                                    class="btn btn-sm btn-info hover-rotate-end m-2">
                                    <i class="ki-outline ki-pencil fs-4"></i>
                                    Edit
                                </a>
                            </div>
                            <!--end::Menu-->
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
            <!--end::Table-->
        </div>
        <!--end::Card body-->
    </div>
    <!--end::Products-->

    @push('footer')
        <script src="{{asset('assets/plugins/custom/datatables/datatables.bundle.js')}}"></script>

        @vite('resources/js/estate/maintenance/list-maintenance.js')
        @vite('resources/js/estate/maintenance/add-maintenance.js')
        {{-- <script src="{{asset('assets/js/custom/apps/estate/maintenance/listing.js')}}"></script>
        <script src="{{asset('assets/js/custom/apps/estate/maintenance/add-maintenance.js')}}"></script> --}}
    @endpush
</x-app-layout>
