@props(['departments', 'workTypes', 'users', 'workflow'])
<!--begin::Modal - Start New Internal Maintenance Modal-->
<x-modal label="New Internal Maintenance Entry" key="kt_modal_add_maintenance"
    class="btn btn-sm btn-primary hover-rotate-end me-2" icon="ki-outline ki-wrench" size="mw-850px">
    <x-slot name="header">New Internal Maintenance Entry</x-slot>

    <x-slot name="headerclosebtn">
        <!--begin::Close-->
        <div class="btn btn-icon btn-sm btn-active-icon-primary" data-kt-maintenance-modal-action="close">
            <i class="ki-outline ki-cross fs-1"></i>
        </div>
        <!--end::Close-->
    </x-slot>

    <!--begin:Form-->
    <form id="kt_modal_add_maintenance_form" class="form" enctype="multipart/form-data">
        @csrf
        <input type="hidden" name="type" value="internal" autocomplete="off">
        <input type="hidden" name="workflow_id" value="{{ $workflow->id }}" autocomplete="off">

        <!--begin::Heading-->
        <div class="mb-13 text-center">
            <!--begin::Title-->
            <h1 class="mb-3">Start New Internal Maintenance</h1>
            <!--end::Title-->
            <!--begin::Description-->
            <div class="text-gray-500 fw-semibold fs-5">Maintenance Requisition Entry</div>
            <!--end::Description-->
        </div>
        <!--end::Heading-->

        <!--begin::Input group : Entry Date & Requesting Officer Name-->
        <div class="row g-9 mb-8">
            <!--begin::Col : Entry Date-->
            <div class="col-md-4 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Entry Date</label>
                <!--begin::Input-->
                <div class="position-relative d-flex align-items-center">
                    <!--begin::Icon-->
                    <div class="symbol symbol-20px me-4 position-absolute ms-4">
                        <span class="symbol-label bg-secondary">
                            <i class="ki-outline ki-element-11"></i>
                        </span>
                    </div>
                    <!--end::Icon-->
                    <!--begin::Datepicker-->
                    <input class="form-control form-control-solid ps-12" placeholder="Select a date" name="entry_date" />
                    <!--end::Datepicker-->
                </div>
                <!--end::Input-->
            </div>
            <!--end::Col-->
            <!--begin::Col : Requesting Officer Name-->
            <div class="col-md-8 fv-row">
                <!--begin::Label-->
                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                    <span class="required">Requesting Officer Name</span>
                </label>
                <!--end::Label-->
                <input type="text" class="form-control form-control-solid" placeholder="Enter requesting officer name"
                    name="requesting_officer_name" />
                <!--end::Col-->
            </div>
            <!--end::Input group-->
        </div>

        <!--begin::Input group : Department & Location-->
        <div class="row g-9 mb-8">
            <!--begin::Col : Department-->
            <div class="col-md-4 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Department</label>
                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true"
                    data-placeholder="Select a department" name="department_id">
                    <option value="">Select a department...</option>
                    @foreach ($departments as $department)
                        @php
                            $selected = $department->id == old('department_id');
                        @endphp
                        <option value="{{ $department->id }}" {{ $selected ? 'selected="selected"' : '' }}>
                            {{ $department->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <!--end::Col-->
            <!--begin::Col : Location-->
            <div class="col-md-8 fv-row">
                <!--begin::Label-->
                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                    <span class="required">Location</span>
                </label>
                <!--end::Label-->
                <input type="text" class="form-control form-control-solid" placeholder="Enter maintenance location"
                    name="location" />
                <!--end::Col-->
            </div>
            <!--end::Col-->
        </div>
        <!--end::Input group-->

        <!--begin::Input group : Work Type & Others (work type)-->
        <div class="row g-9 mb-8">
            <!--begin::Col : Work Type-->
            <div class="col-md-6 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Work Type</label>
                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true"
                    data-placeholder="Select work type" name="work_type_id">
                    <option value="">Select a work type...</option>
                    @foreach ($workTypes as $worktype)
                        @php
                            $selected = $worktype->id == old('worktype_id');
                        @endphp
                            <option value="{{ $worktype->id }}" {{ $selected ? 'selected="selected"' : '' }}>
                                {{ $worktype->name }}
                                @if (str_contains(strtolower($worktype->name), 'other'))
                                    "(Please type)"
                                @endif
                            </option>
                    @endforeach
                </select>
            </div>
            <!--end::Col-->
            <!--begin::Col : Others (work type)-->
            <div class="col-md-6 fv-row">
                <!--begin::Label-->
                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                    <span>Others (work type)</span>
                </label>
                <!--end::Label-->
                <input type="text" class="form-control form-control-solid" placeholder="" name="other_work_type" />
                <!--end::Col-->
            </div>
            <!--end::Col-->
        </div>
        <!--end::Input group-->

        <!--begin::Input group : Description of Work -->
        <div class="d-flex flex-column mb-8 fv-row">
            <label class="required fs-6 fw-semibold mb-2">Description of Work</label>
            <textarea class="form-control form-control-solid" rows="3" name="description"
                placeholder="Type your description of work here"></textarea>
        </div>
        <!--end::Input group-->

        <!--begin::Input group : Proposed Action -->
        <div class="d-flex flex-column mb-8 fv-row">
            <label class="required fs-6 fw-semibold mb-2">Proposed Action</label>
            <textarea class="form-control form-control-solid" rows="2" name="proposed_action"
                placeholder="Type the proposed action here"></textarea>
        </div>
        <!--end::Input group-->

        <!--begin::Input group : Cost Implication & Checked By-->
        <div class="row g-9 mb-8">
            <!--begin::Col : Cost Implication-->
            <div class="col-md-6 fv-row">
                <!--begin::Label-->
                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                    <span class="required">Cost Implication</span>
                </label>
                <!--end::Label-->
                <input type="number" class="form-control form-control-solid" placeholder="" name="cost_implication" />
                <!--end::Col-->
            </div>
            <!--end::Col-->
            <!--begin::Col : Checked By-->
            <div class="col-md-6 fv-row">
                <!--begin::Label-->
                <label class="required d-flex align-items-center fs-6 fw-semibold mb-2">
                    <span>Checked By</span>
                </label>
                <!--end::Label-->
                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true"
                    data-placeholder="Select user" name="checked_by">
                    <option value="">Select a user...</option>
                    @foreach ($users as $user)
                        @php
                            $selected = $user->id == old('user_id');
                        @endphp
                            <option value="{{ $user->id }}" {{ $selected ? 'selected="selected"' : '' }}>
                                {{ $user->fullname }}
                            </option>
                    @endforeach
                </select>
                <!--end::Col-->
            </div>
            <!--end::Col-->
        </div>
        <!--end::Input group-->

        @if ($workflow->attachment_required)
            <!--begin::Input group-->
            <div class="d-flex flex-column align-items-start mb-7">
                <label class="fs-6 fw-semibold form-label mb-4">
                    <span class="required">Support Docs Attachments</span>
                    <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                        data-bs-content="This field is required.">
                        <i class="ki-outline ki-information fs-7"></i>
                    </span>
                </label>
                <div class="mb-2">
                    <label for="supporting_doc_url" class="btn btn-primary d-inline-flex align-items-center gap-2">
                        <i class="ki-outline ki-upload fs-3"></i>
                        <span>Upload Support Docs</span>
                    </label>
                    <input type="file" id="supporting_doc_url" name="supporting_doc_url[]" accept=".png,.jpg,.jpeg,.pdf" multiple class="d-none" />
                </div>
                <div id="supporting-docs-preview" class="d-flex flex-wrap gap-2 mt-2"></div>
                <input type="hidden" name="supporting_doc_urls" id="supporting_doc_urls">
                <div class="form-text">Allowed file types: png, jpg, jpeg, pdf. Size limit is 2MB. Upload up to 10 files.</div>
            </div>
            <!--end::Input group-->
        @endif

        <!--begin::Input group : Notifications SMS Phone In-app -->
        <div class="mb-15 fv-row">
            <!--begin::Wrapper-->
            <div class="d-flex flex-stack">
                <!--begin::Label-->
                <div class="fw-semibold me-5">
                    <label class="fs-6">Notifications</label>
                    <div class="fs-7 text-gray-500">Allow Notifications by Phone or Email or In-app</div>
                </div>
                <!--end::Label-->
                <!--begin::Checkboxes-->
                <div class="d-flex align-items-center">
                    <!--begin::Checkbox-->
                    <label class="form-check form-check-custom form-check-solid me-10">
                        <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]"
                            value="email" disabled />
                        <span class="form-check-label fw-semibold">Email</span>
                    </label>
                    <!--end::Checkbox-->
                    <!--begin::Checkbox-->
                    <label class="form-check form-check-custom form-check-solid me-10">
                        <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]"
                            value="phone" disabled />
                        <span class="form-check-label fw-semibold">Phone</span>
                    </label>
                    <!--end::Checkbox-->
                    <!--begin::Checkbox : In-app -->
                    <label class="form-check form-check-custom form-check-solid me-10">
                        <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]"
                            value="inapp" checked="checked" />
                        <span class="form-check-label fw-semibold">In-app</span>
                    </label>
                    <!--end::Checkbox-->
                </div>
                <!--end::Checkboxes-->
            </div>
            <!--end::Wrapper-->
        </div>
        <!--end::Input group-->

        <!--begin::Actions : SUBMIT/CANCEL BUTTON-->
        <div class="text-center pt-5">
            <button type="reset" class="btn btn-light me-3"
                data-kt-maintenance-modal-action="cancel">Cancel</button>
            <button type="submit" class="btn btn-primary" data-kt-maintenance-modal-action="submit">
                <span class="indicator-label">Submit</span>
                <span class="indicator-progress">Please wait...
                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
            </button>
        </div>
        <!--end::Actions-->

    </form>
    <!--end:Form-->
</x-modal>
<!--end::Add user-->
