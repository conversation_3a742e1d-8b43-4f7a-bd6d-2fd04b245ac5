<!--begin::Add user-->
<x-modal label="New Maintenance" key="kt_modal_add_maintenance" class="btn btn-primary" icon="ki-outline ki-plus" size="mw-650px">
    <x-slot name="header">Start New External Maintenance</x-slot>
     <!--begin:Form-->
    <form id="kt_modal_new_ticket_form" class="form" action="#">

        <!--begin::Heading-->
        <div class="mb-13 text-center">
            <!--begin::Title-->
            <h1 class="mb-3">Start New External Maintenance</h1>
            <!--end::Title-->
            <!--begin::Description-->
            <div class="text-gray-500 fw-semibold fs-5">Maintenance Requisition Entry.
            <a href="" class="fw-bold link-primary">User Help/Support Guidelines</a>.</div>
            <!--end::Description-->
        </div>
        <!--end::Heading-->

        <!--begin::Input group : Entry Date & Requesting Officer Name-->
        <div class="row g-9 mb-8">
            <!--begin::Col : Entry Date-->
            <div class="col-md-4 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Entry Date</label>
                <!--begin::Input-->
                <div class="position-relative d-flex align-items-center">
                    <!--begin::Icon-->
                    <div class="symbol symbol-20px me-4 position-absolute ms-4">
                        <span class="symbol-label bg-secondary">
                            <i class="ki-outline ki-element-11"></i>
                        </span>
                    </div>
                    <!--end::Icon-->
                    <!--begin::Datepicker-->
                    <input class="form-control form-control-solid ps-12" placeholder="Select a date" name="due_date" />
                    <!--end::Datepicker-->
                </div>
                <!--end::Input-->
            </div>
            <!--end::Col-->
            <!--begin::Col : Requesting Officer Name-->
            <div class="col-md-8 fv-row">
            <!--begin::Label-->
                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                    <span class="required">Requesting Officer Name</span>
                    <!-- <span class="ms-2" data-bs-toggle="tooltip" title="Enter requesting officer name">
                        <i class="ki-outline ki-information fs-7"></i>
                    </span> -->
                </label>
                <!--end::Label-->
                <input type="text" class="form-control form-control-solid" placeholder="Enter requesting officer name" name="subject" />
            <!--end::Col-->
        </div>
        <!--end::Input group-->
        </div>

        <!--begin::Input group : Department & Location-->
        <div class="row g-9 mb-8">
            <!--begin::Col : Department-->
            <div class="col-md-4 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Department</label>
                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select a department" name="product">
                    <option value="">Select a department...</option>
                    <option value="1">Estate</option>
                    <option value="2">Audit</option>
                    <option value="3">Accounts</option>
                    <option value="4">Pension Admin</option>
                    <option value="5">Store</option>
                    <option value="6">Supply Chain</option>
                </select>
            </div>
            <!--end::Col-->
            <!--begin::Col : Location-->
            <div class="col-md-8 fv-row">
            <!--begin::Label-->
                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                    <span class="required">Location</span>
                </label>
                <!--end::Label-->
                <input type="text" class="form-control form-control-solid" placeholder="Enter maintenance location" name="subject" />
            <!--end::Col-->
            </div>
            <!--end::Col-->
        </div>
        <!--end::Input group-->

        <!--begin::Input group : Work Type & Others (work type)-->
        <div class="row g-9 mb-8">
            <!--begin::Col : Work Type-->
            <div class="col-md-6 fv-row">
                <label class="required fs-6 fw-semibold mb-2">Work Type</label>
                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select work type" name="product">
                    <option value="">Select a work type...</option>
                    <option value="1">Masonry</option>
                    <option value="2">Carpentry</option>
                    <option value="3">Electrical</option>
                    <option value="4">Mechanical</option>
                    <option value="5">Plumbing</option>
                    <option value="6">Air Conditioning</option>
                    <option value="7">Water Treatment Plant</option>
                    <option value="8">Others (Pls type)</option>
                </select>
            </div>
            <!--end::Col-->
            <!--begin::Col : Others (work type)-->
            <div class="col-md-6 fv-row">
            <!--begin::Label-->
                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                    <span>Others (work type)</span>
                </label>
                <!--end::Label-->
                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
            <!--end::Col-->
            </div>
            <!--end::Col-->
        </div>
        <!--end::Input group-->

        <!--begin::Input group : Description of Work -->
        <div class="d-flex flex-column mb-8 fv-row">
            <label class="fs-6 fw-semibold mb-2">Description of Work</label>
            <textarea class="form-control form-control-solid" rows="3" name="description" placeholder="Type your description of work here"></textarea>
        </div>
        <!--end::Input group-->

        <!--begin::Input group : Proposed Action -->
        <div class="d-flex flex-column mb-8 fv-row">
            <label class="fs-6 fw-semibold mb-2">Proposed Action</label>
            <textarea class="form-control form-control-solid" rows="2" name="description" placeholder="Type the proposed action here"></textarea>
        </div>
        <!--end::Input group-->

        <!--begin::Input group : Cost Implication & Checked By-->
        <div class="row g-9 mb-8">
            <!--begin::Col : Cost Implication-->
            <div class="col-md-6 fv-row">
            <!--begin::Label-->
                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                    <span class="required">Cost Implication</span>
                </label>
                <!--end::Label-->
                <input type="number" class="form-control form-control-solid" placeholder="" name="subject" />
            <!--end::Col-->
            </div>
            <!--end::Col-->
            <!--begin::Col : Checked By-->
            <div class="col-md-6 fv-row">
            <!--begin::Label-->
                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                    <span>Checked By</span>
                </label>
                <!--end::Label-->
                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
            <!--end::Col-->
            </div>
            <!--end::Col-->
        </div>
        <!--end::Input group-->

        <!--begin::Input group : Support Docs Attachments-->
        <div class="fv-row mb-8">
            <label class="fs-6 fw-semibold mb-2">Support Docs Attachments</label>
            <!--begin::Dropzone-->
            <div class="dropzone" id="kt_modal_create_ticket_attachments">
                <!--begin::Message-->
                <div class="dz-message needsclick align-items-center">
                    <!--begin::Icon-->
                    <i class="ki-outline ki-file-up fs-3hx text-primary"></i>
                    <!--end::Icon-->
                    <!--begin::Info-->
                    <div class="ms-4">
                        <h3 class="fs-5 fw-bold text-gray-900 mb-1">Drop files here or click to upload.</h3>
                        <span class="fw-semibold fs-7 text-gray-500">Upload up to 10 files</span>
                    </div>
                    <!--end::Info-->
                </div>
            </div>
            <!--end::Dropzone-->
        </div>
        <!--end::Input group-->

        <!--begin::Input group : Notifications SMS Phone In-app -->
        <div class="mb-15 fv-row">
            <!--begin::Wrapper-->
            <div class="d-flex flex-stack">
                <!--begin::Label-->
                <div class="fw-semibold me-5">
                    <label class="fs-6">Notifications</label>
                    <div class="fs-7 text-gray-500">Allow Notifications by Phone or Email or In-app</div>
                </div>
                <!--end::Label-->
                <!--begin::Checkboxes-->
                <div class="d-flex align-items-center">
                    <!--begin::Checkbox-->
                    <label class="form-check form-check-custom form-check-solid me-10">
                        <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]" value="email" checked="checked" />
                        <span class="form-check-label fw-semibold">Email</span>
                    </label>
                    <!--end::Checkbox-->
                    <!--begin::Checkbox-->
                    <label class="form-check form-check-custom form-check-solid me-10">
                        <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]" value="phone" />
                        <span class="form-check-label fw-semibold">Phone</span>
                    </label>
                    <!--end::Checkbox-->
                    <!--begin::Checkbox : In-app -->
                    <label class="form-check form-check-custom form-check-solid me-10">
                        <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]" value="inapp" />
                        <span class="form-check-label fw-semibold">In-app</span>
                    </label>
                    <!--end::Checkbox-->
                </div>
                <!--end::Checkboxes-->
            </div>
            <!--end::Wrapper-->
        </div>
        <!--end::Input group-->

        <!--begin::Actions : Submit/Cancel Button-->
        <div class="text-center">
            <button type="reset" id="kt_modal_new_ticket_cancel" class="btn btn-light me-3">Cancel</button>
            <button type="submit" id="kt_modal_new_ticket_submit" class="btn btn-primary">
                <span class="indicator-label">Submit</span>
                <span class="indicator-progress">Please wait...
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
            </button>
        </div>
        <!--end::Actions-->

    </form>
    <!--end:Form-->
</x-modal>
<!--end::Add user-->
