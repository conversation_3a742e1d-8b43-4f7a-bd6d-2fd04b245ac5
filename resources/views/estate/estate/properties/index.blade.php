@php
    $breadCrumbs = [
        ['name' => __('Home'), 'url' => route('dashboard')],
        ['name' => __('Estate')],
        ['name' => __('Properties/Estate Encumbrences'), 'url' => route('users.index')],
    ]
@endphp
<x-app-layout :title="__('Properties/Estate Encumbrences')" :breadCrumbs="$breadCrumbs">
    <!--begin::Row-->
        <div class="row gy-5 gx-xl-10">
            <!--begin::Col-->
            <div class="col-sm-6 col-xl-3 mb-xl-10">
                <!--begin::Card widget 2-->
                <div class="card h-lg-100">
                    <!--begin::Body-->
                    <div class="card-body d-flex justify-content-between align-items-start flex-column">
                        <!--begin::Icon-->
                        {{-- <div class="m-0">
                            <i class="ki-outline ki-compass fs-2hx text-gray-600"></i>
                        </div> --}}
                        <!--end::Icon-->
                        <!--begin::Section-->
                        <div class="d-flex flex-column my-7">
                            <!--begin::Number-->
                            <span class="fw-semibold fs-3x text-gray-800 lh-1 ls-n2">30</span>
                            <!--end::Number-->
                            <!--begin::Follower-->
                            <div class="m-0">
                                <span class="fw-semibold fs-6 text-gray-500">Total Litigations</span>
                            </div>
                            <!--end::Follower-->
                        </div>
                        <!--end::Section-->
                        <!--begin::Badge-->
                        <span class="badge badge-light-success fs-base">
                        <i class="ki-outline ki-arrow-up fs-5 text-success ms-n1">2.1%</i></span>
                        <!--end::Badge-->
                    </div>
                    <!--end::Body-->
                </div>
                <!--end::Card widget 2-->
            </div>
            <!--end::Col-->
            <!--begin::Col-->
            <div class="col-sm-6 col-xl-3 mb-xl-10">
                <!--begin::Card widget 2-->
                <div class="card h-lg-100">
                    <!--begin::Body-->
                    <div class="card-body d-flex justify-content-between align-items-start flex-column">
                        <!--begin::Icon-->
                        {{-- <div class="m-0">
                            <i class="ki-outline ki-chart-simple fs-2hx text-gray-600"></i>
                        </div> --}}
                        <!--end::Icon-->
                        <!--begin::Section-->
                        <div class="d-flex flex-column my-7">
                            <!--begin::Number-->
                            <span class="fw-semibold fs-3x text-gray-800 lh-1 ls-n2">03</span>
                            <!--end::Number-->
                            <!--begin::Follower-->
                            <div class="my-1">
                                <span class="fw-semibold fs-6 text-gray-500">Total Arbitrations</span>
                            </div>
                            <!--end::Follower-->
                        </div>
                        <!--end::Section-->
                        <!--begin::Badge-->
                        <span class="badge badge-light-danger fs-base">
                        <i class="ki-outline ki-arrow-up fs-5 text-success ms-n1"></i>2.1%</span>
                        <!--end::Badge-->
                    </div>
                    <!--end::Body-->
                </div>
                <!--end::Card widget 2-->
            </div>
            <!--end::Col-->
            <!--begin::Col-->
            <div class="col-sm-6 col-xl-3 mb-xl-10">
                <!--begin::Card widget 2-->
                <div class="card h-lg-100">
                    <!--begin::Body-->
                    <div class="card-body d-flex justify-content-between align-items-start flex-column">
                        <!--begin::Icon-->
                        {{-- <div class="m-0">
                            <i class="ki-outline ki-check-square fs-2hx text-gray-600"></i>
                        </div> --}}
                        <!--end::Icon-->
                        <!--begin::Section-->
                        <div class="d-flex flex-column my-7">
                            <!--begin::Number-->
                            <span class="fw-semibold fs-3x text-gray-800 lh-1 ls-n2">19</span>
                            <!--end::Number-->
                            <!--begin::Follower-->
                            <div class="m-0">
                                <span class="fw-semibold fs-6 text-gray-500">Out of Court Settlements</span>
                            </div>
                            <!--end::Follower-->
                        </div>
                        <!--end::Section-->
                        <!--begin::Badge-->
                        <span class="badge badge-light-success fs-base">
                        <i class="ki-outline ki-arrow-down fs-5 text-danger ms-n1"></i>0.47%</span>
                        <!--end::Badge-->
                    </div>
                    <!--end::Body-->
                </div>
                <!--end::Card widget 2-->
            </div>
            <!--end::Col-->
            <!--begin::Col-->
            <div class="col-sm-6 col-xl-3 mb-xl-10">
                <!--begin::Card widget 2-->
                <div class="card h-lg-100">
                    <!--begin::Body-->
                    <div class="card-body d-flex justify-content-between align-items-start flex-column">
                        <!--begin::Icon-->
                        {{-- <div class="m-0">
                            <i class="ki-outline ki-check-square fs-2hx text-gray-600"></i>
                        </div> --}}
                        <!--end::Icon-->
                        <!--begin::Section-->
                        <div class="d-flex flex-column my-7">
                            <!--begin::Number-->
                            <span class="fw-semibold fs-3x text-gray-800 lh-1 ls-n2">%19</span>
                            <!--end::Number-->
                            <!--begin::Follower-->
                            <div class="m-0">
                                <span class="fw-semibold fs-6 text-gray-500">Completed Matters</span>
                            </div>
                            <!--end::Follower-->
                        </div>
                        <!--end::Section-->
                        <!--begin::Badge-->
                        <span class="badge badge-light-success fs-base">
                        <i class="ki-outline ki-arrow-down fs-5 text-danger ms-n1"></i>0.47%</span>
                        <!--end::Badge-->
                    </div>
                    <!--end::Body-->
                </div>
                <!--end::Card widget 2-->
            </div>
            <!--end::Col-->
        </div>
    <!--end::Row-->

    <!--begin::Products-->
								<div class="card card-flush border border-gray-300">
									<!--begin::Card header-->
									<div class="card-header align-items-center py-5 gap-2 gap-md-5">
										<!--begin::Card title-->
										<div class="card-title">
											<!--begin::Search-->
											<div class="d-flex align-items-center position-relative my-1">
												<i class="ki-outline ki-magnifier fs-3 position-absolute ms-4"></i>
												<input type="text" data-kt-ecommerce-order-filter="search" class="form-control form-control-solid w-250px ps-12" placeholder="Search Order" />
											</div>
											<!--end::Search-->
										</div>
										<!--end::Card title-->
										<!--begin::Card toolbar-->
										<div class="card-toolbar flex-row-fluid justify-content-end gap-5">
											<!--begin::Flatpickr-->
											<div class="input-group w-250px">
												<input class="form-control form-control-solid rounded rounded-end-0" placeholder="Pick date range" id="kt_ecommerce_sales_flatpickr" />
												<button class="btn btn-icon btn-light" id="kt_ecommerce_sales_flatpickr_clear">
													<i class="ki-outline ki-cross fs-2"></i>
												</button>
											</div>
											<!--end::Flatpickr-->
											<div class="w-100 mw-150px">
												<!--begin::Select2-->
												<select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Status" data-kt-ecommerce-order-filter="status">
													<option></option>
													<option value="all">All</option>
													<option value="Cancelled">Cancelled</option>
													<option value="Completed">Completed</option>
													<option value="Denied">Denied</option>
													<option value="Expired">Expired</option>
													<option value="Failed">Failed</option>
													<option value="Pending">Pending</option>
													<option value="Processing">Processing</option>
													<option value="Refunded">Refunded</option>
													<option value="Delivered">Delivered</option>
													<option value="Delivering">Delivering</option>
												</select>
												<!--end::Select2-->
											</div>


											<!--begin:: Btn add new internal maintenance-->
											<a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_new_ticket" data-bs-toggle="modal"><i class="ki-outline ki-add-files fs-2"></i> Log New Encumbrence</a>

											<!-- <a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_manage_internal" data-bs-toggle="modal"><i class="ki-outline ki-wrench fs-2"></i> Mgt Modal </a> -->

											<!-- <a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_new_ticket" data-bs-toggle="modal"><i class="ki-outline ki-wrench fs-2"></i> XX </a>
											end::Add product-->
										</div>
										<!--end::Card toolbar-->
									</div>
									<!--end::Card header-->
									<!--begin::Card body-->
									<div class="card-body pt-0">
										<!--begin::Table-->
										<table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_sales_table">
											<thead>
												<tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
													<th class="text-start w-10px pe-2">
														<div class="form-check form-check-sm form-check-custom form-check-solid me-3">
															<input class="form-check-input" type="checkbox" data-kt-check="true" data-kt-check-target="#kt_ecommerce_sales_table .form-check-input" value="1" />
														</div>
													</th>
													<th class="min-w-50px">Entry_ID</th>
													<th class="min-w-50px">Entry Date</th>
													<th class="min-w-70px">Property/Estate </th>
													<th class="min-w-50px">Encumb. Type</th>
													<th class="min-w-100px">Encumb. Classification</th>
													<th class="text-end min-w-50px">Assoc. Cost</th>
													<th class="text-end min-w-70px">Status</th>
													<th class="text-center min-w-50px">Actions</th>
												</tr>
											</thead>
											<tbody class="fw-semibold text-gray-600">
												<tr>
													<td class="text-start">
														<div class="form-check form-check-sm form-check-custom form-check-solid">
															<input class="form-check-input" type="checkbox" value="1" />
														</div>
													</td>
													<td class="text-start" data-kt-ecommerce-order-filter="order_id">
														<a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13760</a>
													</td>
													<td class="text-start" data-order="2024-08-07">
														<span class="fw-bold">07/08/2024</span>
													</td>
													<td class="text-start"><span class="fw-bold">Plot No 9, Isa Boss Street, Bwari Abuja</span></td>
													<td class="text-start"> <span class="fw-bold fs-7">Litigation</span> </td>
													<td>
														<div class="d-flex align-items-center">
															<!--begin:: Avatar -->
															<div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
																	<div class="symbol-label fs-3 bg-light-danger text-danger">B</div>
															</div>
															<!--end::Avatar-->
															<div class="ms-5">
																<!--begin::Title-->
																<a href="apps/user-management/users/view.html" class="text-gray-800 text-hover-primary fs-5 fw-bold">Bill/Fees Issue</a>
																<!--end::Title-->
															</div>
														</div>
													</td>
													<td class="text-end pe-0">
														<span class="fw-bold">&#8358 250,000.00</span>
													</td>
													<td class="text-end pe-0" data-order="Completed">
														<!--begin::Badges-->
														<div class="badge badge-light-success">Completed</div>
														<!--end::Badges-->
													</td>
													<td class="text-end">
														<a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
														<i class="ki-outline ki-down fs-5 ms-1"></i></a>
														<!--begin::Menu-->
														<div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">

															<!--begin::Menu item-->
															<div><a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_manage_internal" data-bs-toggle="modal"><i class="ki-outline ki-setting-4 fs-4"></i> Manage</a></div>

															<div class="menu-item px-3">
															</div>

															<div><a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-4"></i>Print/View</a></div>
															<!--end::Menu item-->

														</div>
														<!--end::Menu-->

													</td>
												</tr>
												<tr>
													<td class="text-start">
														<div class="form-check form-check-sm form-check-custom form-check-solid">
															<input class="form-check-input" type="checkbox" value="1" />
														</div>
													</td>
													<td class="text-start" data-kt-ecommerce-order-filter="order_id">
														<a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13765</a>
													</td>
													<td class="text-start" data-order="2024-07-31">
														<span class="fw-bold">31/07/2024</span>
													</td>
													<td class="text-start"> <span class="fw-bold">Block 257, Ilesha Estate, Lugbe - Abuja</span> </td>
													<td class="text-start"> <span class="fw-bold fs-7">Arbitration</span> </td>
													<td>
														<div class="d-flex align-items-center">
															<!--begin:: Avatar -->
															<div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
																<a href="apps/user-management/users/view.html">
																	<div class="symbol-label fs-3 bg-light-primary text-primary">L</div>
																</a>
															</div>
															<!--end::Avatar-->
															<div class="ms-5">
																<!--begin::Title-->
																<a href="apps/user-management/users/view.html" class="text-gray-800 text-hover-primary fs-5 fw-bold">Land Dispute</a>
																<!--end::Title-->
															</div>
														</div>
													</td>
													<td class="text-end pe-0">
														<span class="fw-bold">$455,000.00</span>
													</td>
													<td class="text-end pe-0" data-order="Delivered">
														<!--begin::Badges-->
														<div class="badge badge-light-success">Delivered</div>
														<!--end::Badges-->
													</td>
													<td class="text-end">
														<a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
														<i class="ki-outline ki-down fs-5 ms-1"></i></a>
														<!--begin::Menu-->
														<div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="apps/ecommerce/sales/details.html" class="menu-link px-3">View</a>
															</div>
															<!--end::Menu item-->
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="apps/ecommerce/sales/edit-order.html" class="menu-link px-3">Edit</a>
															</div>
															<!--end::Menu item-->
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="#" class="menu-link px-3" data-kt-ecommerce-order-filter="delete_row">Delete</a>
															</div>
															<!--end::Menu item-->
														</div>
														<!--end::Menu-->
													</td>
												</tr>
												<tr>
													<td class="text-start">
														<div class="form-check form-check-sm form-check-custom form-check-solid">
															<input class="form-check-input" type="checkbox" value="1" />
														</div>
													</td>
													<td class="text-start" data-kt-ecommerce-order-filter="order_id">
														<a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13768</a>
													</td>
													<td class="text-start" data-order="2024-07-30">
														<span class="fw-bold">30/07/2024</span>
													</td>
													<td class="text-start"> <span class="fw-bold">Block 258, Ilesha Estate, Lugbe - Abuja</span> </td>
													<td class="text-start"> <span class="fw-bold fs-7">Arbitration</span> </td>
													<td>
														<div class="d-flex align-items-center">
															<!--begin:: Avatar -->
															<div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
																	<div class="symbol-label fs-3 bg-light-danger text-danger">B</div>
															</div>
															<!--end::Avatar-->
															<div class="ms-5">
																<!--begin::Title-->
																<a href="apps/user-management/users/view.html" class="text-gray-800 text-hover-primary fs-5 fw-bold">Border Dispute</a>
																<!--end::Title-->
															</div>
														</div>
													</td>
													<td class="text-end pe-0">
														<span class="fw-bold">$00.00</span>
													</td>
													<td class="text-end pe-0" data-order="Refunded">
														<!--begin::Badges-->
														<div class="badge badge-light-info">Refunded</div>
														<!--end::Badges-->
													</td>
													<td class="text-end">
														<a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
														<i class="ki-outline ki-down fs-5 ms-1"></i></a>
														<!--begin::Menu-->
														<div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="apps/ecommerce/sales/details.html" class="menu-link px-3">View</a>
															</div>
															<!--end::Menu item-->
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="apps/ecommerce/sales/edit-order.html" class="menu-link px-3">Edit</a>
															</div>
															<!--end::Menu item-->
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="#" class="menu-link px-3" data-kt-ecommerce-order-filter="delete_row">Delete</a>
															</div>
															<!--end::Menu item-->
														</div>
														<!--end::Menu-->
													</td>
												</tr>
												<tr>
													<td class="text-start">
														<div class="form-check form-check-sm form-check-custom form-check-solid">
															<input class="form-check-input" type="checkbox" value="1" />
														</div>
													</td>
													<td class="text-start" data-kt-ecommerce-order-filter="order_id">
														<a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13769</a>
													</td>
													<td class="text-start" data-order="2024-07-27">
														<span class="fw-bold">27/07/2024</span>
													</td>
													<td class="text-start"> <span class="fw-bold">House No 34, Titi Street, V.I Lagos</span> </td>
													<td class="text-start"> <span class="fw-bold fs-7">Others</span> </td>
													<td>
														<div class="d-flex align-items-center">
															<!--begin:: Avatar -->
															<div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
																	<div class="symbol-label fs-3 bg-light-danger text-danger">T</div>
															</div>
															<!--end::Avatar-->
															<div class="ms-5">
																<!--begin::Title-->
																<a href="apps/user-management/users/view.html" class="text-gray-800 text-hover-primary fs-5 fw-bold">Estate Title</a>
																<!--end::Title-->
															</div>
														</div>
													</td>
													<td class="text-end pe-0">
														<span class="fw-bold">$00.00</span>
													</td>
													<td class="text-end pe-0" data-order="Failed">
														<!--begin::Badges-->
														<div class="badge badge-light-danger">Failed</div>
														<!--end::Badges-->
													</td>
													<td class="text-end">
														<a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
														<i class="ki-outline ki-down fs-5 ms-1"></i></a>
														<!--begin::Menu-->
														<div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="apps/ecommerce/sales/details.html" class="menu-link px-3">View</a>
															</div>
															<!--end::Menu item-->
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="apps/ecommerce/sales/edit-order.html" class="menu-link px-3">Edit</a>
															</div>
															<!--end::Menu item-->
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="#" class="menu-link px-3" data-kt-ecommerce-order-filter="delete_row">Delete</a>
															</div>
															<!--end::Menu item-->
														</div>
														<!--end::Menu-->
													</td>
												</tr>
												<tr>
													<td class="text-start">
														<div class="form-check form-check-sm form-check-custom form-check-solid">
															<input class="form-check-input" type="checkbox" value="1" />
														</div>
													</td>
													<td class="text-start" data-kt-ecommerce-order-filter="order_id">
														<a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13770</a>
													</td>
													<td class="text-start" data-order="2024-08-01">
														<span class="fw-bold">01/08/2024</span>
													</td>
													<td class="text-start"> <span class="fw-bold">No 6612, Calabar Road, Calabar.</span> </td>
													<td class="text-start"> <span class="fw-bold fs-7">Others</span> </td>
													<td>
														<div class="d-flex align-items-center">
															<!--begin:: Avatar -->
															<div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
																<a href="#">
																	<div class="symbol-label fs-3 bg-light-warning text-warning">B</div>
																</a>
															</div>
															<!--end::Avatar-->
															<div class="ms-5">
																<!--begin::Title-->
																<a href="#" class="text-gray-800 text-hover-primary fs-5 fw-bold">Bill/Fees Issue</a>
																<!--end::Title-->
															</div>
														</div>
													</td>
													<td class="text-end pe-0">
														<span class="fw-bold">$00.00</span>
													</td>
													<td class="text-end pe-0" data-order="Processing">
														<!--begin::Badges-->
														<div class="badge badge-light-primary">Processing</div>
														<!--end::Badges-->
													</td>
													<td class="text-end">
														<a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
														<i class="ki-outline ki-down fs-5 ms-1"></i></a>
														<!--begin::Menu-->
														<div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="apps/ecommerce/sales/details.html" class="menu-link px-3">View</a>
															</div>
															<!--end::Menu item-->
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="apps/ecommerce/sales/edit-order.html" class="menu-link px-3">Edit</a>
															</div>
															<!--end::Menu item-->
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="#" class="menu-link px-3" data-kt-ecommerce-order-filter="delete_row">Delete</a>
															</div>
															<!--end::Menu item-->
														</div>
														<!--end::Menu-->
													</td>
												</tr>
												<tr>
													<td class="text-start">
														<div class="form-check form-check-sm form-check-custom form-check-solid">
															<input class="form-check-input" type="checkbox" value="1" />
														</div>
													</td>
													<td class="text-start" data-kt-ecommerce-order-filter="order_id">
														<a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13780</a>
													</td>
													<td class="text-start" data-order="2024-07-18">
														<span class="fw-bold">18/07/2024</span>
													</td>
													<td class="text-start"> <span class="fw-bold">Block 22, Flat 3, Bege HE, Nassarawa State.</span> </td>
													<td class="text-start"> <span class="fw-bold fs-7">Litigation</span> </td>
													<td>
														<div class="d-flex align-items-center">
															<!--begin:: Avatar -->
															<div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
																<a href="apps/user-management/users/view.html">
																	<div class="symbol-label fs-3 bg-light-info text-info">B</div>
																</a>
															</div>
															<!--end::Avatar-->
															<div class="ms-5">
																<!--begin::Title-->
																<a href="apps/user-management/users/view.html" class="text-gray-800 text-hover-primary fs-5 fw-bold">Bill/Fees Issue</a>
																<!--end::Title-->
															</div>
														</div>
													</td>
													<td class="text-end pe-0">
														<span class="fw-bold">$00.00</span>
													</td>
													<td class="text-end pe-0" data-order="Denied">
														<!--begin::Badges-->
														<div class="badge badge-light-danger">Denied</div>
														<!--end::Badges-->
													</td>
													<td class="text-end">
														<a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
														<i class="ki-outline ki-down fs-5 ms-1"></i></a>
														<!--begin::Menu-->
														<div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="apps/ecommerce/sales/details.html" class="menu-link px-3">View</a>
															</div>
															<!--end::Menu item-->
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="apps/ecommerce/sales/edit-order.html" class="menu-link px-3">Edit</a>
															</div>
															<!--end::Menu item-->
															<!--begin::Menu item-->
															<div class="menu-item px-3">
																<a href="#" class="menu-link px-3" data-kt-ecommerce-order-filter="delete_row">Delete</a>
															</div>
															<!--end::Menu item-->
														</div>
														<!--end::Menu-->
													</td>
												</tr>


											</tbody>
										</table>
										<!--end::Table-->
									</div>
									<!--end::Card body-->
								</div>
								<!--end::Products-->

								<!--begin::Modal - Add new external xxxxxxxxxxxx-->
								<div class="modal fade" id="kt_modal_add_newexternalmaint" tabindex="-1" aria-hidden="true">

								</div>
								<!--end::Modal - Add xxxxxxx-->



								<!--begin::Modal - Start New Internal Maintenance Modal-->
								<div class="modal fade" id="kt_modal_new_ticket" tabindex="-1" aria-hidden="true">
									<!--begin::Modal dialog-->
									<div class="modal-dialog modal-dialog-centered mw-750px">
										<!--begin::Modal content-->
										<div class="modal-content rounded">
											<!--begin::Modal header-->
											<div class="modal-header pb-0 border-0 justify-content-end">
												<!--begin::Close-->
												<div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
													<i class="ki-outline ki-cross fs-1"></i>
												</div>
												<!--end::Close-->
											</div>
											<!--begin::Modal header-->
											<!--begin::Modal body-->
											<div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-15">
												<!--begin:Form-->
												<form id="kt_modal_new_ticket_form" class="form" action="#">

													<!--begin::Heading-->
													<div class="mb-13 text-center">
														<!--begin::Title-->
														<h1 class="mb-3">Register New Encumbrence</h1>
														<!--end::Title-->
														<!--begin::Description-->
														<div class="text-gray-500 fw-semibold fs-5">Encumbrence Entry Form</div>
														<!--end::Description-->
													</div>
													<!--end::Heading-->

													<!--begin::Input group : Entry Date & Property/Estate-->
													<div class="row g-9 mb-8">
														<!--begin::Col : Entry Date-->
														<div class="col-md-4 fv-row">
															<label class="required fs-6 fw-semibold mb-2">Entry Date</label>
															<!--begin::Input-->
															<div class="position-relative d-flex align-items-center">
																<!--begin::Icon-->
																<div class="symbol symbol-20px me-4 position-absolute ms-4">
																	<span class="symbol-label bg-secondary">
																		<i class="ki-outline ki-element-11"></i>
																	</span>
																</div>
																<!--end::Icon-->
																<!--begin::Datepicker-->
																<input class="form-control form-control-solid ps-12" placeholder="Select a date" name="due_date" />
																<!--end::Datepicker-->
															</div>
															<!--end::Input-->
														</div>
														<!--end::Col-->
														<!--begin::Col : Property/Estate-->
														<div class="col-md-8 fv-row">
														<!--begin::Label-->
															<label class="d-flex align-items-center fs-6 fw-semibold mb-2">
																<span class="required">Property/Estate</span>
																<!-- <span class="ms-2" data-bs-toggle="tooltip" title="Enter requesting officer name">
																	<i class="ki-outline ki-information fs-7"></i>
																</span> -->
															</label>
															<!--end::Label-->
															<input type="text" class="form-control form-control-solid" placeholder="Enter Property/Estate" name="subject" />
														<!--end::Col-->
													</div>
													<!--end::Input group-->
													</div>

													<!--begin::Input group : Type of Encumbrence & Name/Location of Action-->
													<div class="row g-9 mb-8">
														<!--begin::Col : Type of Encumbrence-->
														<div class="col-md-4 fv-row">
															<label class="required fs-6 fw-semibold mb-2">Type of Encumbrence</label>
															<select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select a type" name="product">
																<option value="">Select a type...</option>
																<option value="1">Litigation</option>
																<option value="2">Arbitration</option>
																<option value="3">Out of Court Settlements</option>
																<option value="4">Others</option>
															</select>
														</div>
														<!--end::Col-->
														<!--begin::Col : Location-->
														<div class="col-md-8 fv-row">
														<!--begin::Label-->
															<label class="d-flex align-items-center fs-6 fw-semibold mb-2">
																<span class="required">Name/Location of Action</span>
															</label>
															<!--end::Label-->
															<input type="text" class="form-control form-control-solid" placeholder="Enter location detail" name="subject" />
														<!--end::Col-->
														</div>
														<!--end::Col-->
													</div>
													<!--end::Input group-->

													<!--begin::Input group : Matter Classification & Others (matter class type) -->
													<div class="row g-9 mb-8">
														<!--begin::Col : Matter Classification-->
														<div class="col-md-6 fv-row">
															<label class="required fs-6 fw-semibold mb-2">Encumbrence Classification</label>
															<select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select class" name="product">
																<option value="">Select class...</option>
																<option value="1">Land Dispute</option>
																<option value="2">Border Dispute</option>
																<option value="3">Ownership Matter</option>
																<option value="4">Documentation Issues</option>
																<option value="5">Statutory Bills/Fees Issue</option>
																<option value="8">Others (Pls type)</option>
															</select>
														</div>
														<!--end::Col-->
														<!--begin::Col : Others (matter class type)-->
														<div class="col-md-6 fv-row">
														<!--begin::Label-->
															<label class="d-flex align-items-center fs-6 fw-semibold mb-2">
																<span>Others (matter type)</span>
															</label>
															<!--end::Label-->
															<input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
														<!--end::Col-->
														</div>
														<!--end::Col-->
													</div>
													<!--end::Input group-->

													<!--begin::Input group : Description of Encumbrence & Parties Involved -->
													<div class="d-flex flex-column mb-8 fv-row">
														<label class="fs-6 fw-semibold mb-2">Description of Encumbrence  & Parties Involved</label>
														<textarea class="form-control form-control-solid" rows="2" name="description" placeholder="Type your description here"></textarea>
													</div>
													<!--end::Input group-->

													<!--begin::Input group : Proposed Action -->
													<div class="d-flex flex-column mb-8 fv-row">
														<label class="fs-6 fw-semibold mb-2">Proposed Action</label>
														<textarea class="form-control form-control-solid" rows="2" name="description" placeholder="Type the proposed action here"></textarea>
													</div>
													<!--end::Input group-->

													<!--begin::Input group : Cost Implication/Involved -->
													<div class="row g-9 mb-8">
														<!--begin::Col : Cost Implication-->
														<div class="col-md-6 fv-row">
														<!--begin::Label-->
															<label class="d-flex align-items-center fs-6 fw-semibold mb-2">
																<span class="required">Cost Implication/Cost Involved</span>
															</label>
															<!--end::Label-->
															<input type="number" class="form-control form-control-solid" placeholder="" name="subject" />
														<!--end::Col-->
														</div>
														<!--end::Col-->
														<!--begin::Col : -->

														<!--end::Col-->
													</div>
													<!--end::Input group-->

													<!--begin::Input group : Support Docs Attachments-->
													<div class="fv-row mb-8">
														<label class="fs-6 fw-semibold mb-2">Related/Supporting Docs Attachments</label>
														<!--begin::Dropzone-->
														<div class="dropzone" id="kt_modal_create_ticket_attachments">
															<!--begin::Message-->
															<div class="dz-message needsclick align-items-center">
																<!--begin::Icon-->
																<i class="ki-outline ki-file-up fs-3hx text-primary"></i>
																<!--end::Icon-->
																<!--begin::Info-->
																<div class="ms-4">
																	<h3 class="fs-5 fw-bold text-gray-900 mb-1">Drop files here or click to upload.</h3>
																	<span class="fw-semibold fs-7 text-gray-500">Upload up to 10 files</span>
																</div>
																<!--end::Info-->
															</div>
														</div>
														<!--end::Dropzone-->
													</div>
													<!--end::Input group-->


													<!--begin::Actions : Submit/Cancel Button-->
													<div class="text-center">
														<button type="reset" id="kt_modal_new_ticket_cancel" class="btn btn-light me-3">Cancel</button>
														<button type="submit" id="kt_modal_new_ticket_submit" class="btn btn-primary">
															<span class="indicator-label">Submit/Commit</span>
															<span class="indicator-progress">Please wait...
															<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
														</button>
													</div>
													<!--end::Actions-->

												</form>
												<!--end:Form-->
											</div>
											<!--end::Modal body-->
										</div>
										<!--end::Modal content-->
									</div>
									<!--end::Modal dialog-->
								</div>
								<!--end::Modal - Start New Internal Maintenance Modal-->



								<!--begin::Modal - Manage Existing External Maintenance Modal-->
								<div class="modal fade" id="kt_modal_manage_internal" tabindex="-1" aria-hidden="true">
									<!--begin::Modal dialog-->
									<div class="modal-dialog modal-dialog-centered mw-1000px">
										<!--begin::Modal content-->
										<div class="modal-content rounded">
											<!--begin::Modal header-->
											<div class="modal-header pb-0 border-0 justify-content-end">
												<!--begin::Close-->
												<div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
													<i class="ki-outline ki-cross fs-1"></i>
												</div>
												<!--end::Close-->
											</div>
											<!--begin::Modal header-->
											<!--begin::Modal body-->
											<div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-0">
												<!--begin:Form-->
												<form id="kt_modal_new_ticket_form" class="form" action="#">

												<!--begin::Heading-->
												<div class="mb-13 text-center">
													<!--begin::Title-->
													<h1 class="mb-3">Manage Encumbrences: #20240082</h1>
													<!--end::Title-->
													<!--begin::Description-->
													<div class="text-gray-500 fw-semibold fs-5">Perform actions on Encumbrences entry</div>
													<!--end::Description-->
												</div>
												<!--end::Heading-->

												<!--begin:: Summary Table-->
												<div class="table-responsive">
													<table class="table table-bordered table-rounded border-gray-300">
														<thead>
															<!--begin::Label-->
															<span class="badge badge-light-success">
																<div class="fw-semibold fs-6"><i class="ki-outline ki-document fs-3 text-success me-2"></i>Entry Details:</div>
															</span>
															<!--end::Label-->
														</thead>
														<tbody>
															<tr>
																<td  class="col-md-3"><label class="fs-6 fw-semibold">Entry Date:</label></td>
																<td> 1-October-2024</td>
															</tr>
															<tr>
																<td  class="col-md-3"><label class="fs-6 fw-semibold">Requesting Officer:</label></td>
																<td> Aliyu Olawale</td>
															</tr>
															<tr>
																<td  class="col-md-3"><label class="fs-6 fw-semibold">Department:</label></td>
																<td> Pension Admin</td>
															</tr>
															<tr>
																<td  class="col-md-3"><label class="fs-6 fw-semibold">Location:</label></td>
																<td> NPFL HQ, Ibi Close, Garki 2, Abuja</td>
															</tr>
															<tr>
																<td  class="col-md-3"><label class="fs-6 fw-semibold">Work Type:</label></td>
																<td> Plumbing</td>
															</tr>
															<tr>
																<td  class="col-md-3"><label class="fs-6 fw-semibold">Description of Work:</label></td>
																<td> Repairs of 2nd floor toilet pipings</td>
															</tr>
															<tr>
																<td  class="col-md-3"><label class="fs-6 fw-semibold">Proposed Action:</label></td>
																<td> Conduct assesment, replace all weak materials & test</td>
															</tr>
															<tr>
																<td  class="col-md-3"><label class="fs-6 fw-semibold">Cost Implication:</label></td>
																<td> 12,340,79.50</td>
															</tr>
															<tr>
																<td  class="col-md-3"><label class="fs-6 fw-semibold">Checked By:</label></td>
																<td> Mr Haliru KSA</td>
															</tr>

														</tbody>
													</table>
												</div>
												<!--end:: Summary Table-->


												<!--begin::Progress Bar-->
												<div class="vis-group" style="height: 50px;">
													<div class="vis-item vis-range vis-readonly" style="transform: translateX(10px); width: 98%; top: 17.5px;">
														<div class="vis-item-overflow">
															<div class="vis-item-content" style="transform: translateX(0px);">
																<div class="rounded-pill bg-light-primary d-flex align-items-center position-relative h-25px w-100 p-2 overflow-hidden">
																	<div class="position-absolute rounded-pill d-block bg-danger start-0 top-0 h-100 z-index-1" style="width:60%;"></div>

																	<div class="d-flex align-items-center position-relative z-index-2">
																		<a href="#" class="fw-bold text-white text-hover-dark"> Status: Process in Progress</a>
																	</div>

																	<div class="d-flex flex-center bg-body rounded-pill fs-7 fw-bolder ms-auto h-100 px-3 position-relative z-index-2"> 60% </div>

																</div>
															</div>
														</div>
													</div>
												</div>
												<!--end::Progress Bar-->

												</form>
												<!--end:Form-->
											</div>
											<!--end::Modal body-->

											<div class="d-flex flex-wrap flex-stack m-8 align-items-start">

												<!--begin::Stat-->
												<div class="border border-gray-300 border-solid rounded min-w-450px py-3 px-4 me-6 mb-8 w-23">
													<!--begin::Label-->
													<span class="badge badge-light-success">
														<div class="fw-semibold fs-6"><i class="ki-outline ki-document fs-3 text-success me-2"></i>Action/Movements History:</div>
													</span>
													<!--end::Label-->

														<div class="d-flex flex-column mb-8 fv-row ">
															<!--begin::Card body-->
															<div class="card-body pt-0">
																<!--begin::Table-->
																<table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_sales_table">
																	<thead>
																		<tr class="text-start text-gray-600 fw-bold fs-7 text-uppercase gs-0">
																			<th class="w-10px">S/N</th>
																			<th class="text-start w-20px">Date</th>
																			<th class="text-start min-w-50px">Action</th>
																			<th class="text-start min-w-50px">Staff</th>
																		</tr>
																	</thead>
																	<tbody class="fw-semibold text-gray-600">
																		<tr>
																			<td class="text-start" data-kt-ecommerce-order-filter="order_id">
																				<span class="fw-normal">1</span>
																			</td>
																			<td class="text-start" data-order="2024-08-07">
																				<span class="fw-normal">07/08/2024</span>
																			</td>
																			<td class="text-start" data-order="2024-08-11">
																				<span class="badge badge-light-warning">Approved for Review</span>
																			</td>
																			<td class="text-start pe-0">
																				<span class="fw-normal">Ibrahim A</span>
																			</td>
																			<td class="text-end">
																				<button type="button" class="btn btn-sm btn-icon btn-light btn-active-light-primary toggle h-25px w-25px" data-kt-table-widget-4="expand_row">
																					<i class="ki-outline ki-plus fs-4 m-0 toggle-off"></i>
																					<i class="ki-outline ki-minus fs-4 m-0 toggle-on"></i>
																				</button>
																			</td>


																		</tr>
																		<tr>
																			<td class="text-start" data-kt-ecommerce-order-filter="order_id">
																				<span class="fw-normal">2</span>
																			</td>
																			<td class="text-start" data-order="2024-08-07">
																				<span class="fw-normal">07/08/2024</span>
																			</td>
																			<td class="text-start" data-order="2024-08-11">
																				<span class="badge badge-light-success">Approved & Escalate</span>
																			</td>
																			<td class="text-start pe-0">
																				<span class="fw-normal">Ibrahim A</span>
																			</td>
																		</tr>
																		<tr>
																			<td class="text-start" data-kt-ecommerce-order-filter="order_id">
																				<span class="fw-normal">3</span>
																			</td>
																			<td class="text-start" data-order="2024-08-07">
																				<span class="fw-normal">07/08/2024</span>
																			</td>
																			<td class="text-start" data-order="2024-08-11">
																				<span class="badge badge-light-info">Reviewed</span>
																			</td>
																			<td class="text-start pe-0">
																				<span class="fw-normal">Ibrahim A</span>
																			</td>
																		</tr>
																		<tr>
																			<td class="text-start" data-kt-ecommerce-order-filter="order_id">
																				<span class="fw-normal">4</span>
																			</td>
																			<td class="text-start" data-order="2024-08-07">
																				<span class="fw-normal">07/08/2024</span>
																			</td>
																			<td class="text-start" data-order="2024-08-11">
																				<span class="badge badge-light-danger">Closed/KIV</span>
																			</td>
																			<td class="text-start pe-0">
																				<div class="fw-normal">Ibrahim A</div>
																			</td>
																		</tr>

																	</tbody>
																</table>
																<!--end::Table-->
															</div>
															<!--end::Card body-->
														</div>

													<br><br>
													<!-- <a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-2"></i>View/Share/Print</a> -->

												</div>
												<!--end::Stat-->

												<!--begin::Stat-->
												<div class="border border-gray-300 border-solid rounded min-w-450px py-3 px-4 me-6 mb-3 w-23">
													<!--begin::Label-->
													<span class="badge badge-light-primary">
														<div class="fw-semibold fs-6"><i class="ki-outline ki-tablet-down fs-3 text-primary me-2"></i>Manage/Update:</div>
													</span>
													<!--end::Label-->
													<br><br>

														<!--begin::Input group : Select Action-->
														<div class="d-flex flex-column mb-8 fv-row">
															<!--begin::Col : Action-->
																<label class="required fs-6 fw-semibold mb-2">Action</label>
																<select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select action" name="product">
																	<option value="">Select a action ...</option>
																	<option value="1">Approve & escalate</option>
																	<option value="2">Re-analyse</option>
																	<option value="3">Keep in view</option>
																	<option value="4">Reject & de-escalate</option>
																	<option value="5">Close process</option>
																	<option value="6">Other actions</option>
																</select>
															<!--end::Col-->
														</div>
														<!--end::Input group-->

														<!--begin::Input group : Comment/Notes -->
														<div class="d-flex flex-column mb-8 fv-row">
															<label class="fs-6 fw-semibold mb-2">Comment/Notes</label>
															<textarea class="form-control form-control-solid" rows="2" name="description" placeholder="Type additonal comment/notes here"></textarea>
														</div>
														<!--end::Input group-->

														<!--begin::Input group : Support Docs Attachments-->
														<div class="fv-row mb-8">
															<label class="fs-6 fw-semibold mb-2">Support Docs Attachments</label>
															<!--begin::Dropzone-->
															<div class="dropzone" id="kt_modal_create_ticket_attachments">
																<!--begin::Message-->
																<div class="dz-message needsclick align-items-center">
																	<!--begin::Icon-->
																	<i class="ki-outline ki-file-up fs-3hx text-primary"></i>
																	<!--end::Icon-->
																	<!--begin::Info-->
																	<div class="ms-4">
																		<h3 class="fs-5 fw-bold text-gray-900 mb-1">Drop files here or click to upload.</h3>
																		<span class="fw-semibold fs-7 text-gray-500">Upload up to 10 files</span>
																	</div>
																	<!--end::Info-->
																</div>
															</div>
															<!--end::Dropzone-->
														</div>
														<!--end::Input group-->

														<!--begin::Input group : Escalate/de-escalate to-->
														<div class="d-flex flex-column mb-8 fv-row">
															<!--begin::Col : Escalate/de-escalate-->
																<label class="required fs-6 fw-semibold mb-2">Escalate/de-escalate to</label>
																<select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select action" name="product">
																	<option value="">Select ...</option>
																	<option value="0">None</option>
																	<option value="1">OIC (Ibrahim Dasuma)</option>
																	<option value="2">MP (Suleiman Okama)</option>
																	<option value="3">EDI</option>
																</select>
															<!--end::Col-->
														</div>
														<!--end::Input group-->

														<a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-file-added fs-2"></i>Save/Commit</a>

												</div>
												<!--end::Stat-->

											</div>





										</div>
										<!--end::Modal content-->
									</div>
									<!--end::Modal dialog-->
								</div>
								<!--end::Modal - Manage Existing External Maintenance Modal-->
</x-app-layout>
