<!--begin::Chat drawer-->
<div id="kt_drawer_chat" class="bg-body" data-kt-drawer="true" data-kt-drawer-name="chat" data-kt-drawer-activate="true"
    data-kt-drawer-overlay="true" data-kt-drawer-width="{default:'300px', 'md': '500px'}" data-kt-drawer-direction="end"
    data-kt-drawer-toggle="#kt_drawer_chat_toggle" data-kt-drawer-close="#kt_drawer_chat_close">
    <!--begin::Messenger-->
    <div class="card w-100 border-0 rounded-0" id="kt_drawer_chat_messenger">
        <!--begin::Card header-->
        <div class="card-header pe-5" id="kt_drawer_chat_messenger_header">
            <!--begin::Title-->
            <div class="card-title">
                <!--begin::User-->
                <div class="d-flex justify-content-center flex-column me-3">
                    <a href="#" class="fs-4 fw-bold text-gray-900 text-hover-primary me-1 mb-2 lh-1">Notifications</a>
                </div>
                <!--end::User-->
            </div>
            <!--end::Title-->
            <!--begin::Card toolbar-->
            <div class="card-toolbar">
                <!--begin::Close-->
                <div class="btn btn-sm btn-icon btn-active-color-primary" id="kt_drawer_chat_close">
                    <i class="ki-outline ki-cross-square fs-2"></i>
                </div>
                <!--end::Close-->
            </div>
            <!--end::Card toolbar-->
        </div>
        <!--end::Card header-->
        <!--begin::Card body-->
        <div class="card-body" id="kt_drawer_chat_messenger_body">
            <!--begin::Messages-->
            <div class="scroll-y me-n5 pe-5" data-kt-element="messages" data-kt-scroll="true"
                data-kt-scroll-activate="true" data-kt-scroll-height="auto"
                data-kt-scroll-dependencies="#kt_drawer_chat_messenger_header, #kt_drawer_chat_messenger_footer"
                data-kt-scroll-wrappers="#kt_drawer_chat_messenger_body" data-kt-scroll-offset="0px">
                @foreach (auth()->user()->unreadNotifications as $notification)
                <!--end::Item-->
                <div class="d-flex align-items-center mb-8">
                    <!--begin::Bullet-->
                    <span class="bullet bullet-vertical h-40px bg-success"></span>
                    <!--end::Bullet-->
                    <!--begin::Checkbox-->
                    <div class="form-check form-check-custom form-check-solid mx-5">
                        <input class="form-check-input notification-checkbox" type="checkbox" value="" data-notification-id="{{ $notification->id }}" />
                    </div>
                    <!--end::Checkbox-->
                    <!--begin::Description-->
                    <div class="flex-grow-1">
                        <a href="#" class="text-gray-800 text-hover-primary fw-bold fs-6">{{ $notification->data['message'] }}</a>
                        <span class="text-muted fw-semibold d-block">{{ $notification->created_at->diffForHumans() }}</span>
                    </div>
                    <!--end::Description-->
                    <span class="badge badge-light-success fs-8 fw-bold">New</span>
                </div>
                @endforeach
            </div>
            <!--end::Messages-->
        </div>
        <!--end::Card body-->
        <!--begin::Card footer-->
        <div class="card-footer pt-4" id="kt_drawer_chat_messenger_footer">
            <!--begin:Toolbar-->
            <div class="row">
                <div class="col-4">
                    <div class="w-full btn btn-sm btn-light btn-color-muted btn-active-light-success px-4 py-4">
                        <i class="ki-outline ki-check-square-text-2 fs-2"></i>
                        <span id="selected-count">0 Read</span>
                    </div>
                </div>
                <div class="col-8">
                    <button class="btn btn-primary w-100 text-center" id="kt_widget_5_load_more_btn">
                        <span class="indicator-label">Mark selected as Read</span>
                        <span class="indicator-progress">Loading...
                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                    </button>
                </div>
            </div>
            <!--end::Toolbar-->
        </div>
        <!--end::Card footer-->
    </div>
    <!--end::Messenger-->
</div>
<!--end::Chat drawer-->

<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
{{-- <script src="/js/shared/notification.js"></script> --}}
@vite('resources/js/shared/notification.js')
