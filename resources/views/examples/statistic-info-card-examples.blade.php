{{-- StatisticInfoCard Component Usage Examples --}}

<div class="p-8 bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">StatisticInfoCard Component Examples</h1>

        {{-- Basic Usage --}}
        <section class="mb-12">
            <h2 class="text-2xl font-semibold text-gray-800 mb-6">Basic Usage</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <livewire:statistic-info-card
                    icon="heroicon-o-users"
                    title="Total Users"
                    subtitle="1,234"
                    color="blue"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-currency-dollar"
                    title="Revenue"
                    subtitle="$45,678"
                    color="green"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-chart-bar"
                    title="Orders"
                    subtitle="892"
                    color="purple"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-exclamation-triangle"
                    title="Issues"
                    subtitle="23"
                    color="red"
                />
            </div>
        </section>

        {{-- With Trends --}}
        <section class="mb-12">
            <h2 class="text-2xl font-semibold text-gray-800 mb-6">With Trend Indicators</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <livewire:statistic-info-card
                    icon="heroicon-o-arrow-trending-up"
                    title="Sales Growth"
                    countUpValue="$12,345"
                    color="green"
                    :show-trend="true"
                    :trend-value="12.5"
                    trend-direction="up"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-arrow-trending-down"
                    title="Bounce Rate"
                    subtitle="23.4%"
                    color="red"
                    :show-trend="true"
                    :trend-value="5.2"
                    trend-direction="down"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-minus"
                    title="Conversion Rate"
                    subtitle="3.2%"
                    color="yellow"
                    :show-trend="true"
                    :trend-value="0.0"
                    trend-direction="neutral"
                />
            </div>
        </section>

        {{-- With Links --}}
        <section class="mb-12">
            <h2 class="text-2xl font-semibold text-gray-800 mb-6">With Action Links</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <livewire:statistic-info-card
                    icon="heroicon-o-document-text"
                    title="Reports"
                    subtitle="45"
                    color="indigo"
                    link="/reports"
                    link-text="View All"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-bell"
                    title="Notifications"
                    subtitle="12"
                    color="yellow"
                    link="/notifications"
                    link-text="Manage"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-cog-6-tooth"
                    title="Settings"
                    subtitle="8"
                    color="gray"
                    link="/settings"
                    link-text="Configure"
                />
            </div>
        </section>

        {{-- Advanced Examples --}}
        <section class="mb-12">
            <h2 class="text-2xl font-semibold text-gray-800 mb-6">Advanced Examples</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <livewire:statistic-info-card
                    icon="heroicon-o-chart-pie"
                    title="Monthly Revenue"
                    subtitle="$89,432"
                    color="green"
                    :show-trend="true"
                    :trend-value="18.7"
                    trend-direction="up"
                    link="/analytics/revenue"
                    link-text="View Details"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-user-group"
                    title="Active Users"
                    subtitle="2,847"
                    color="blue"
                    :show-trend="true"
                    :trend-value="7.3"
                    trend-direction="up"
                    link="/users/active"
                    link-text="Manage Users"
                />
            </div>
        </section>

        {{-- Different Color Schemes --}}
        <section class="mb-12">
            <h2 class="text-2xl font-semibold text-gray-800 mb-6">Color Variations</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <livewire:statistic-info-card
                    icon="heroicon-o-heart"
                    title="Blue"
                    subtitle="100"
                    color="blue"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-heart"
                    title="Green"
                    subtitle="200"
                    color="green"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-heart"
                    title="Red"
                    subtitle="300"
                    color="red"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-heart"
                    title="Yellow"
                    subtitle="400"
                    color="yellow"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-heart"
                    title="Purple"
                    subtitle="500"
                    color="purple"
                />

                <livewire:statistic-info-card
                    icon="heroicon-o-heart"
                    title="Indigo"
                    subtitle="600"
                    color="indigo"
                />
            </div>
        </section>

        {{-- Usage Code Examples --}}
        <section class="mb-12">
            <h2 class="text-2xl font-semibold text-gray-800 mb-6">Code Examples</h2>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Usage</h3>
                <pre class="bg-gray-50 rounded-lg p-4 text-sm overflow-x-auto"><code>&lt;livewire:statistic-info-card
    icon="heroicon-o-users"
    title="Total Users"
    subtitle="1,234"
    color="blue"
/&gt;</code></pre>

                <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">With Trends and Links</h3>
                <pre class="bg-gray-50 rounded-lg p-4 text-sm overflow-x-auto"><code>&lt;livewire:statistic-info-card
    icon="heroicon-o-chart-bar"
    title="Monthly Sales"
    subtitle="$45,678"
    color="green"
    :show-trend="true"
    :trend-value="12.5"
    trend-direction="up"
    link="/sales/monthly"
    link-text="View Report"
/&gt;</code></pre>

                <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">In Controller/Component</h3>
                <pre class="bg-gray-50 rounded-lg p-4 text-sm overflow-x-auto"><code>// In a Livewire component or controller
public function render()
{
    return view('dashboard', [
        'userCount' => User::count(),
        'revenue' => Order::sum('total'),
        'growthRate' => $this->calculateGrowthRate(),
    ]);
}</code></pre>
            </div>
        </section>
    </div>
</div>
