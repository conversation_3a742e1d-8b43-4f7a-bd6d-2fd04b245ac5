@props([
    'picture' => auth()->user()->profile_picture ?? 'default.jpg',
])
@php
    $breadCrumbs = [
        ['name' => __('Home'), 'url' => route('dashboard')],
        ['name' => __('My Profile')],
    ]
@endphp
<x-app-layout :title="__('Dashboard')" :breadCrumbs="$breadCrumbs">

    <form class="form-horizontal" role="form"  method="post" id="profile_edit_form">
        <!--begin::details Attention Message Area View-->
        <div class="card mb-5 mb-xl-10" id="kt_profile_details_view">
                <!--begin::Notice-->
                <div class="notice d-flex bg-light-warning rounded border-warning border border-dashed p-6">
                    <!--begin::Icon-->
                    <i class="ki-outline ki-information fs-2tx text-warning me-4"></i>
                    <!--end::Icon-->
                    <!--begin::Wrapper-->
                    <div class="d-flex flex-stack flex-grow-1">
                        <!--begin::Content-->
                        <div class="fw-semibold">
                            <h4 class="text-gray-900 fw-bold">Attention!</h4>
                            <div class="fs-6 text-gray-700">Please ensure that all your details below are correct. Also note that all fields in orange can not be changed by the user.
                            </div>
                        </div>
                        <!--end::Content-->
                    </div>
                    <!--end::Wrapper-->
                </div>
                <!--end::Notice-->
        </div>
        <!--end::details View-->

        <div class="col-xl-6 mb-xl-10">
            <!--begin::Input group= Passport photo-->
            <div class="col-lg-8">
                <!--begin::Image input-->
                <div class="image-input image-input-outline" data-kt-image-input="true" style="background-image: url('assets/media/svg/avatars/blank.svg')">
                    <!--begin::Preview existing avatar-->
                    <div class="image-input-wrapper w-200px h-200px" style="background-image: url({{ asset(\App\Http\Controllers\Shared\UserManagement\UserController::AVATAR_PATH . $picture) }})"></div>
                    <!--end::Preview existing avatar-->
                    <!--begin::Label-->

                    <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" aria-label="Change avatar" data-bs-original-title="Change avatar" data-kt-initialized="1">
                        <i class="ki-outline ki-pencil fs-7"></i>

                        <!--begin::Inputs-->
                        <input type="file" name="avatar" accept=".jpg, .jpeg">
                        <input type="hidden" name="avatar_remove">
                        <!--end::Inputs-->

                    </label>
                    <!--end::Label-->

                    <!--begin::Cancel-->
                    <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" aria-label="Cancel avatar" data-bs-original-title="Cancel avatar" data-kt-initialized="1">
                        <i class="ki-outline ki-cross fs-2"></i>
                    </span>
                    <!--end::Cancel-->

                    <!--begin::Remove-->
                    <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" aria-label="Remove avatar" data-bs-original-title="Remove avatar" data-kt-initialized="1">
                        <i class="ki-outline ki-cross fs-2"></i>
                    </span>
                    <!--end::Remove-->

                </div>
                <!--end::Image input-->
                <!--begin::Hint-->
                <div class="form-text">Allowed file types: jpg, jpeg.</div>
                <!--end::Hint-->
            </div>

            <div id="uploadStatus"></div>
            <!--end::Input group=-->
        </div>

        <!--begin::Row-->
        <div class="row gy-5 g-xl-10">

            <!--begin:: Personal Info Col-->
            <div class="col-xl-6 mb-xl-10">

                <div class="card card-flush h-lg-100">
                    <!--begin::Header-->
                    <div class="card-header cursor-pointer">
                        <!--begin::Title-->
                        <h3 class="card-title align-items-start flex-column">
                            <span class="card-label fw-bold text-gray-700">Personal/Profile Info:</span>
                        </h3>
                        <!--end::Title-->
                    </div>
                    <div class="app-navbar-separator separator d-none d-lg-flex"></div>
                    <!--end::Header-->

                    <!--begin::Body-->
                    <div class="card-body pt-5 ps-6">

                        <!--begin::Input group= User ID-->
                        <div class="fv-row mb-4">
                            <input type="text" placeholder="User ID No" name="user_idno" id="user_idno"  autocomplete="off" class="form-control bg-transparent" value="<?php echo htmlentities(auth()->user()->id); ?>"   disabled />
                        <!-- <div class="text-gray-500 pt-2 fw-semibold fs-6">Payroll No.</div> -->
                        </div>
                        <!--end::Input group=-->

                        <!--begin::Input group= Full name-->
                        <div class="fv-row mb-4">
                            <input type="text" placeholder="Full Name" name="full_name" id="full_name"  autocomplete="off" class="form-control bg-transparent" value="<?php echo htmlentities(auth()->user()->fullname); ?>"  disabled/>
                        </div>
                        <!--end::Input group=-->

                        <!--begin::Input group= Email address-->
                        <div class="fv-row mb-4">
                            <input type="text" placeholder="Email Address" name="email_add" id="email_add"  autocomplete="off" class="form-control bg-transparent" value="<?php echo htmlentities(auth()->user()->email); ?>"  disabled/>
                        </div>
                        <!--end::Col-->

                        <!--begin::Input group= Phone no-->
                        <div class="fv-row mb-4">
                            <input type="number" placeholder="Phone No" name="user_phone" id="user_phone" autocomplete="off" class="form-control bg-transparent" value="<?php echo htmlentities(auth()->user()->phone_no); ?>" />
                        </div>
                        <!--end::Col-->

                        <!--begin::Input group= Gender & Marital -->
                        <div class="col-lg-12">
                                <!--begin::Row-->
                                <div class="row">

                                    <!--begin::Gender Col-->
                                        <div class="col-lg-6 fv-row mb-4">
                                        <select class="form-select" aria-label="Select Gender" name="gender" id="gender"   data-placeholder="Select Gender...">
                                            <option value="0">Select Gender...</option>
                                            <option value="1" <?php if(1 == '1') {echo("selected");}?> >Male</option>
                                            <option value="2" <?php if(2 == '2') {echo("selected");}?> >Female</option>
                                        </select>
                                        </div>
                                    <!--end::Col-->

                                    <!--begin::Col-->
                                        <div class="col-lg-6 fv-row mb-4">
                                        <select class="form-select" aria-label="Select Marital Status" name="mstatus" id="mstatus"   data-placeholder="Select Marital Status...">
                                            <option value="0">Select Marital Status...</option>
                                            <option value="1" <?php if('s' == '1'){echo("selected");}?> >Single</option>
                                            <option value="2" <?php if('m' == '2'){echo("selected");}?> >Married</option>
                                            <option value="3" <?php if('w' == '3'){echo("selected");}?> >Widowed</option>
                                            <option value="4" <?php if('d' == '4'){echo("selected");}?> >Divorcee</option>
                                        </select>
                                        </div>
                                    <!--end::Col-->

                                </div>
                                <!--end::Row-->
                            </div>

                        <!--begin::Input group= Rank-->
                        <div class="fv-row mb-4">
                            <input type="text" placeholder="Rank" name="rank" id="rank"  autocomplete="off" class="form-control bg-transparent" value="<?php echo htmlentities(auth()->user()->rank?->name); ?>"/>
                        </div>
                        <!--end::Input group=-->


                        <!-- <div id="kt_charts_widget_5" class="min-h-auto"></div> -->
                                                                            <!--begin::Save Changes Button -->
                        <div class="card mb-5 mb-xl-10">
                                <!--<form class="form">-->
                                    <!--begin::Actions Save Changes Button-->
                                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                                        <button type="submit" class="btn btn-primary" id="btn_profile_update_submit">Save Changes</button>
                                    </div>
                                    <!--end::Card footer
                                </form>-->
                        </div>
                        <!--end::Save Button -->


                    </div>
                    <!--end::Body-->

                </div>

            </div>
            <!--end::Col-->



            <!--begin:: Password Reset Col-->
            <div class="col-xl-6 mb-xl-10">

                <div class="card card-flush h-lg-100">
                    <!--begin::Header-->
                    <div class="card-header cursor-pointer">
                        <!--begin::Title-->
                        <h3 class="card-title align-items-start flex-column">
                            <span class="card-label fw-bold text-gray-700">Password Reset:</span>
                        </h3>
                        <!--end::Title-->
                    </div>
                    <div class="app-navbar-separator separator d-none d-lg-flex"></div>
                    <!--end::Header-->

                    <!--begin::Body-->
                    <div class="card-body pt-5 ps-6">

                        <!--begin::Input group : Current password-->
                        <div class="fv-row mb-4">
                            <input type="text" placeholder="Current Password" name="current_password" id="current_password"  autocomplete="off" class="form-control bg-transparent"  value="" />
                        </div>
                        <!--end::Input group=-->

                        <!--begin::Input group : New password-->
                        <div class="fv-row mb-8" data-kt-password-meter="true">
                            <!--begin::Wrapper-->
                            <div class="mb-1">
                                <!--begin::Input wrapper-->
                                <div class="position-relative mb-3">
                                    <input class="form-control bg-transparent" type="password" placeholder="Password" name="first_password" id="first_password"  autocomplete="off" />
                                    <span class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2" data-kt-password-meter-control="visibility">
                                        <i class="ki-outline ki-eye-slash fs-2"></i>
                                        <i class="ki-outline ki-eye fs-2 d-none"></i>
                                    </span>
                                </div>
                                <!--end::Input wrapper-->

                                <!--begin::Meter-->
                                <div class="d-flex align-items-center mb-3" data-kt-password-meter-control="highlight">
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px"></div>
                                </div>
                                <!--end::Meter-->
                            </div>
                            <!--end::Wrapper-->
                            <!--begin::Hint-->
                            <div class="text-muted">Use 8 or more characters with a mix of letters, numbers & symbols.</div>
                            <!--end::Hint-->
                        </div>
                        <!--end::Input group=-->

                        <!--end::Input group : Repeat password-->
                        <div class="fv-row mb-8">
                            <!--begin::Repeat Password-->
                            <input type="password" placeholder="Repeat Password" name="confirm_password"  id="confirm_password" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Repeat Password-->
                        </div>
                        <!--end::Input group=-->

                        <!--begin::Save Changes Button -->
                        <div class="card mb-5 mb-xl-10">
                                <!--<form class="form">-->
                                    <!--begin::Actions Save Changes Button-->
                                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                                        <button type="submit" class="btn btn-primary" id="btn_change_password_submit">Change Password</button>
                                    </div>
                                    <!--end::Card footer
                                </form>-->
                        </div>
                        <!--end::Save Button -->

                    </div>
                    <!--end::Body-->
                </div>

            </div>
            <!--end::Col-->

        </div>
        <!--end::Row-->

        <template>
            <!--begin::Row-->
            <div class="row gy-5 g-xl-10">

                <!--begin:: Access Roles-->
                <div class="col-xl-6 mb-xl-10">

                    <div class="card card-flush h-lg-100">
                        <!--begin::Header-->
                        <div class="card-header cursor-pointer">
                            <!--begin::Title-->
                            <h3 class="card-title align-items-start flex-column">
                                <span class="card-label fw-bold text-gray-700">Access/Permission:</span>
                            </h3>
                            <!--end::Title-->
                        </div>
                        <div class="app-navbar-separator separator d-none d-lg-flex"></div>
                        <!--end::Header-->

                        <!--begin::Body-->
                        <div class="card-body pt-5 ps-6">



                        </div>
                        <!--end::Body-->

                    </div>

                </div>
                <!--end::Col-->



                <!--begin:: User Logs Col-->
                <div class="col-xl-6 mb-xl-10">

                    <div class="card card-flush h-lg-100">
                        <!--begin::Header-->
                        <div class="card-header cursor-pointer">
                            <!--begin::Title-->
                            <h3 class="card-title align-items-start flex-column">
                                <span class="card-label fw-bold text-gray-700">User Logs:</span>
                            </h3>
                            <!--end::Title-->
                        </div>
                        <div class="app-navbar-separator separator d-none d-lg-flex"></div>
                        <!--end::Header-->

                        <!--begin::Body-->
                        <div class="card-body pt-5 ps-6">


                        </div>
                        <!--end::Body-->
                    </div>

                </div>
                <!--end::Col-->

            </div>
            <!--end::Row-->
        </template>
    </form>

    @push('footer')
        <script src="{{asset('assets/plugins/custom/datatables/datatables.bundle.js')}}"></script>
        <script src="{{asset('assets/js/custom/apps/profile/my-profile.js')}}"></script>
    @endpush
</x-app-layout>
