<!--begin::Modal - Start New Internal Maintenance Modal-->
<x-modal label="Add New Workflow" key="kt_modal_add_workflow" class="btn btn-sm btn-primary hover-rotate-end me-2"
    icon="ki-outline ki-data" size="mw-850px">
    <x-slot name="header">Add New Workflow</x-slot>

    <x-slot name="headerclosebtn">
        <!--begin::Close-->
        <div class="btn btn-icon btn-sm btn-active-icon-primary" data-kt-workflow-modal-action="close">
            <i class="ki-outline ki-cross fs-1"></i>
        </div>
        <!--end::Close-->
    </x-slot>

    <!--begin:Form-->
    <form id="kt_modal_add_workflow_form" class="form" action="#">
        @csrf

        <div class="d-flex flex-column px-5 px-lg-10">

            <!--begin::Input group : MODULE & SUB MODULE-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="fs-6 fw-semibold form-label mb-2">
                    <span class="required">Page</span>
                    <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                        data-bs-content="Page is required">
                        <i class="ki-outline ki-information fs-7"></i>
                    </span>
                </label>
                <!--end::Label-->
                <!--begin::Input-->
                <select name="menu_id" aria-label="Select page" data-control="select3" data-placeholder="Select page..."
                    class="form-select" required>
                    <option value="">Select page...</option>
                    @foreach ($subModules as $subModule)
                        @if (!in_array($subModule->code, ['user-management', 'workflow']))
                            <optgroup label="{{ $subModule->name }}">
                                @foreach ($subModule->menus as $menu)
                                    @if (!empty($menu->route) && strpos(Str::lower($menu->title), 'dashboard') === false && $menu->is_hidden == false)
                                        @php
                                            $selected = $menu->id == old('menu_id');
                                        @endphp
                                        <option value="{{ $menu->id }}" {{ $selected ? 'selected="selected"' : '' }}>
                                            {{ $menu->title }}
                                        </option>
                                    @endif
                                @endforeach
                            </optgroup>
                        @endif
                    @endforeach
                </select>
                <!--end::Input-->
            </div>
            <!--end::Input group-->

            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="fs-6 fw-semibold form-label mb-2">
                    <span class="required">Action/Process</span>
                    <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                        data-bs-content="Action is required">
                        <i class="ki-outline ki-information fs-7"></i>
                    </span>
                </label>
                <!--end::Label-->
                <!--begin::Input-->
                <textarea class="form-control form-control-solid" rows="2" name="action"
                    placeholder="Type the workflow action here">{{ old('action') }}</textarea>
                <!--end::Input-->
            </div>
            <!--end::Input group-->

            <!--begin::Input group : STAGE & STAGE %-->
            <div class="row g-9 mb-8">
                <!--begin::Col : Stage-->
                <div class="col-md-6 fv-row">
                    <!--begin::Label-->
                    <label class="fs-6 fw-semibold form-label mb-2">
                        <span class="required">Process Stage</span>
                        <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                            data-bs-content="Stage is required">
                            <i class="ki-outline ki-information fs-7"></i>
                        </span>
                    </label>
                    <!--end::Label-->
                    <input type="number" class="form-control form-control-solid" placeholder="" name="stage_number"
                        id="stage_number" />
                    <!--end::Col-->
                </div>
                <!--end::Col-->

                <!--begin::Col : Stage %-->
                <div class="col-md-6 fv-row">
                    <!--begin::Label-->
                    <label class="fs-6 fw-semibold form-label mb-2">
                        <span class="required">Stage Percentage</span>
                        <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                            data-bs-content="Percentage is required">
                            <i class="ki-outline ki-information fs-7"></i>
                        </span>
                    </label>
                    <!--end::Label-->
                    <input type="number" class="form-control form-control-solid" placeholder=""
                        name="stage_progress_percent" id="stage_progress_percent" />
                    <!--end::Col-->
                </div>
                <!--end::Col-->
            </div>
            <!--end::Input group-->

            <!--begin::Input group : ATTACH_REQUIRED & DUE DAYS-->
            <div class="row g-9 mb-8">
                <!--begin::Col : Work Type-->
                <div class="col-md-4 fv-row">
                    <!--begin::Label-->
                    <label class="fs-6 fw-semibold form-label mb-2">
                        <span class="required">Document Attachment</span>
                        <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                            data-bs-content="This field is required">
                            <i class="ki-outline ki-information fs-7"></i>
                        </span>
                    </label>
                    <!--end::Label-->
                    <select class="form-select" aria-label="Select an option" name="attachment_required"
                        id="attachment_required">
                        <option value="">Select a requirement...</option>
                        <option value="0">No</option>
                        <option value="1">Yes</option>
                    </select>
                </div>
                <!--end::Col-->

                <!--begin::Col : DUE DAYS-->
                <div class="col-md-4 fv-row">
                    <!--begin::Label-->
                    <label class="fs-6 fw-semibold form-label mb-2">
                        <span class="required">Due Days</span>
                        <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                            data-bs-content="This field is required">
                            <i class="ki-outline ki-information fs-7"></i>
                        </span>
                    </label>
                    <!--end::Label-->
                    <input type="number" class="form-control form-control-solid" placeholder="" name="due_in_days"
                        id="due_in_days" />
                    <!--end::Col-->
                </div>
                <!--end::Col-->

                <!--begin::Col : Work Type-->
                <div class="col-md-4 fv-row">
                    <!--begin::Label-->
                    <label class="fs-6 fw-semibold form-label mb-2">
                        <span class="required">Status</span>
                        <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                            data-bs-content="This field is required">
                            <i class="ki-outline ki-information fs-7"></i>
                        </span>
                    </label>
                    <!--end::Label-->
                    <select class="form-select" aria-label="Select a status" name="is_active" id="is_active">
                        <option value="">Select a status...</option>
                        @foreach (['Inactive', 'Active'] as $index => $value)
                            @php
                                $selected = $index == old('is_active');
                            @endphp
                            <option value="{{ $index }}" {{ $selected ? 'selected="selected"' : '' }}>
                                {{ $value }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <!--end::Col-->

            </div>
            <!--end::Input group-->

            <!--begin::Input group : ACTORS & NOTIFS -->
            <div class="row g-9 mb-8">

                <div class="col-md-6 fv-row">
                    <!--begin::Label-->
                    <label class="fs-6 fw-semibold form-label mb-2">
                        <span class="required">Actors</span>
                        <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                            data-bs-content="Actors are required">
                            <i class="ki-outline ki-information fs-7"></i>
                        </span>
                    </label>
                    <!--end::Label-->
                    <!--begin::Table wrapper-->
                    <div class="table-responsive">
                        <!--begin::Table-->
                        <table class="table align-middle table-row-dashed fs-6 gy-5">
                            <!--begin::Table body-->
                            <tbody class="text-gray-600 fw-semibold">
                                @forelse($ranks as $rank)
                                    <tr>
                                        <td class="text-gray-800">
                                            {{ ucfirst($rank->code) }}<br>
                                        </td>
                                        <td class="p-0">
                                            <input class="form-check-input" type="checkbox" value="{{ $rank->id }}"
                                                name="handlers[]" />
                                        </td>
                                    </tr>
                                @empty
                                    <div class="text-center p-12 uppercase tracking-widest text-gray-500">No ranks found.
                                    </div>
                                @endforelse
                            </tbody>
                            <!--end::Table body-->
                        </table>
                        <!--end::Table-->
                    </div>
                    <!--end::Table wrapper-->
                </div>
                <!--end::Input-->

                <!--begin::Col : Stage %-->
                <div class="col-md-6 fv-row">
                    <!--begin::Label-->
                    <label class="fs-6 fw-semibold form-label mb-2">
                        <span class="required">Notifiers</span>
                        <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                            data-bs-content="Actors are required">
                            <i class="ki-outline ki-information fs-7"></i>
                        </span>
                    </label>
                    <!--end::Label-->
                    <!--begin::Table wrapper-->
                    <div class="table-responsive">
                        <!--begin::Table-->
                        <table class="table align-middle table-row-dashed fs-6 gy-5">
                            <!--begin::Table body-->
                            <tbody class="text-gray-600 fw-semibold">
                                @forelse($ranks as $rank)
                                    <tr>
                                        <td class="text-gray-800">
                                            {{ ucfirst($rank->code) }}<br>
                                        </td>
                                        <td class="p-0">
                                            <input class="form-check-input" type="checkbox" value="{{ $rank->id }}"
                                                name="observers[]" />
                                        </td>
                                    </tr>
                                @empty
                                    <div class="text-center p-12 uppercase tracking-widest text-gray-500">No ranks found.
                                    </div>
                                @endforelse
                            </tbody>
                            <!--end::Table body-->
                        </table>
                        <!--end::Table-->
                    </div>
                    <!--end::Table wrapper-->
                    <!--end::Input-->
                </div>
                <!--end::Col-->
            </div>
            <!--end::Input group-->

            <!--begin::Actions : SUBMIT/CANCEL BUTTON-->
            <div class="text-center pt-15">
                <button type="reset" class="btn btn-light me-3" data-kt-workflow-modal-action="cancel">Cancel</button>
                <button type="submit" class="btn btn-primary" data-kt-workflow-modal-action="submit">
                    <span class="indicator-label">Submit</span>
                    <span class="indicator-progress">Please wait...
                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                </button>
            </div>
            <!--end::Actions-->
        </div>

    </form>
    <!--end:Form-->
</x-modal>
<!--end::Add user-->
