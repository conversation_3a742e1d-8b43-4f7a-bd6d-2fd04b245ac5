@php
    $breadCrumbs = [
        ['name' => __('Home'), 'url' => route('dashboard')],
        ['name' => __('Tenancy')],
        ['name' => __('Rent Demand Notice (RDN)'), 'url' => route('workflows.index')],
    ]
@endphp

<x-app-layout :title="__('Rent Demand Notice (RDN)')" :breadCrumbs="$breadCrumbs">
	<!--begin:: Module Dashboard-->
    <div class="d-flex flex-wrap flex-stack my-8">
        <!--begin::Wrapper-->
        <div class="d-flex flex-column flex-grow-1 pe-8">
            <!--begin::Stats-->
            <div class="d-flex flex-wrap">

                <!--begin::Stat : border-gray-600 border-dashed-->
                <div class="border border-gray-400 rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-primary me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="500" data-kt-countup-prefix="$" data-kt-initialized="1">257</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-primary">
                    <div class="fw-semibold fs-6 ">Upcoming Rent this Months</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->
                <!--begin::Stat-->
                <div class="border border-gray-400 border-solid rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-success me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="80" data-kt-initialized="1">109</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-success">
                    <div class="fw-semibold fs-6 ">Due & Pending</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->
                <!--begin::Stat-->
                <div class="border border-gray-400 border-solid rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-info me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="80" data-kt-initialized="1">65</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-info">
                    <div class="fw-semibold fs-6 ">Follow Ups</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->
                <!--begin::Stat-->
                <div class="border border-gray-300 rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-warning me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="60" data-kt-countup-prefix="%" data-kt-initialized="1">221</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-warning">
                    <div class="fw-semibold fs-6 ">RDN Delivered</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->
                <!--begin::Stat-->
                <div class="border border-gray-300 rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-danger me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="60" data-kt-countup-prefix="%" data-kt-initialized="1">112</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-danger">
                    <div class="fw-semibold fs-6 ">Due in 60 Days</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->
                <!--begin::Stat-->
                <div class="border border-gray-300 rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-danger me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="60" data-kt-countup-prefix="%" data-kt-initialized="1">27</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-danger">
                    <div class="fw-semibold fs-6 ">Due in 90 Days</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->


            </div>
            <!--end::Stats-->
        </div>
        <!--end::Wrapper-->
    </div>
    <!--end:: Module Dashboard-->

    <!--begin::Products-->
            <div class="card card-flush border border-gray-300">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <!--begin::Search-->
                        <div class="d-flex align-items-center position-relative my-1">
                            <i class="ki-outline ki-magnifier fs-3 position-absolute ms-4"></i>
                            <input type="text" data-kt-ecommerce-order-filter="search" class="form-control form-control-solid w-250px ps-12" placeholder="Search " />
                        </div>
                        <!--end::Search-->
                    </div>
                    <!--end::Card title-->
                    <!--begin::Card toolbar-->
                    <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
                        <!--begin::Flatpickr-->
                        <div class="input-group w-250px">
                            <input class="form-control form-control-solid rounded rounded-end-0" placeholder="Pick date range" id="kt_ecommerce_sales_flatpickr" />
                            <button class="btn btn-icon btn-light" id="kt_ecommerce_sales_flatpickr_clear">
                                <i class="ki-outline ki-cross fs-2"></i>
                            </button>
                        </div>
                        <!--end::Flatpickr-->
                        <div class="w-100 mw-150px">
                            <!--begin::Select2-->
                            <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Status" data-kt-ecommerce-order-filter="status">
                                <option></option>
                                <option value="all">All</option>
                                <option value="Cancelled">Cancelled</option>
                                <option value="Completed">Completed</option>
                                <option value="Denied">Denied</option>
                                <option value="Expired">Expired</option>
                                <option value="Failed">Failed</option>
                                <option value="Pending">Pending</option>
                                <option value="Processing">Processing</option>
                                <option value="Refunded">Refunded</option>
                                <option value="Delivered">Delivered</option>
                                <option value="Delivering">Delivering</option>
                            </select>
                            <!--end::Select2-->
                        </div>

                        <!--begin:: Btn add new internal maintenance-->
                        <a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_new_ticket" data-bs-toggle="modal"><i class="ki-outline ki-add-files fs-2"></i> Process Rent Demand Notice (RDN)</a>
                    </div>
                    <!--end::Card toolbar-->
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <!--begin::Table-->
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_sales_table">
                        <thead>
                            <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                                <th class="text-start w-10px pe-2">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                        <input class="form-check-input" type="checkbox" data-kt-check="true" data-kt-check-target="#kt_ecommerce_sales_table .form-check-input" value="1" />
                                    </div>
                                </th>
                                <th class="min-w-50px">Alloc. ID</th>
                                <th class="min-w-50px">Alloc. Date</th>
                                <th class="min-w-70px">Tenancy Period </th>
                                <th class="min-w-50px">Tenant Code/Name</th>
                                <th class="min-w-100px">Estate Code/Name</th>
                                <th class="text-end min-w-50px">Rent Rate</th>
                                <th class="min-w-50px">Due Date</th>
                                <th class="min-w-70px">Status</th>
                                <th class="text-center min-w-50px">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">

                            <tr>
                                <td class="text-start">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">
                                        <input class="form-check-input" type="checkbox" value="1" />
                                    </div>
                                </td>
                                <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                    <a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13760</a>
                                </td>
                                <td class="text-start" data-order="2024-08-07">
                                    <span class="fw-bold  fs-7">07/08/2024</span>
                                </td>
                                <td class="text-start"><span class="fw-bold fs-7">1-Jan-2024 to 31-Dec-2024</span></td>
                                <td class="text-start"> <span class="fw-bold fs-7">TNT.23198 / Mubarak Kere</span> </td>
                                <td class="text-start"> <span class="fw-bold fs-7">EST.13.782 / No 93, Balale Street, Garki 2, Abuja</span> </td>
                                <td class="text-end pe-0">
                                    <span class="fw-bold fs-7">&#8358 250,000</span>
                                </td>
                                <td class="text-start" data-order="2024-08-07"><span class="fw-bold  fs-7">07/08/2024</span></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <!--begin:: Avatar -->
                                        <div class="symbol symbol-circle symbol-40px overflow-hidden me-1">
                                                <div class="symbol-label fs-3 bg-light-warning text-warning">P</div>
                                        </div>
                                        <!--end::Avatar-->
                                        <div class="badge badge-light-warning">Pending</div>
                                    </div>
                                </td>
                                <td class="text-end">
                                    <a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
                                    <i class="ki-outline ki-down fs-5 ms-1"></i></a>
                                    <!--begin::Menu-->
                                    <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-150px py-4" data-kt-menu="true">

                                        <!--begin::Menu item-->
                                        <div><a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-4"></i>Print/View RDN</a></div>
                                        <!--end::Menu item-->

                                    </div>
                                    <!--end::Menu-->

                                </td>
                            </tr>
                            <tr>
                                <td class="text-start">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">
                                        <input class="form-check-input" type="checkbox" value="1" />
                                    </div>
                                </td>
                                <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                    <a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13760</a>
                                </td>
                                <td class="text-start" data-order="2024-08-07">
                                    <span class="fw-bold  fs-7">07/08/2024</span>
                                </td>
                                <td class="text-start"><span class="fw-bold fs-7">1-Jan-2024 to 31-Dec-2024</span></td>
                                <td class="text-start"> <span class="fw-bold fs-7">TNT.2222 / Dorcas Ijemudiga</span> </td>
                                <td class="text-start"> <span class="fw-bold fs-7">EST.13.222 / No 94, Balale Street, Garki 2, Abuja</span> </td>
                                <td class="text-end pe-0">
                                    <span class="fw-bold fs-7">&#8358 250,000</span>
                                </td>
                                <td class="text-start" data-order="2024-08-07"><span class="fw-bold  fs-7">07/08/2024</span></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <!--begin:: Avatar -->
                                        <div class="symbol symbol-circle symbol-40px overflow-hidden me-1">
                                                <div class="symbol-label fs-3 bg-light-info text-info">G</div>
                                        </div>
                                        <!--end::Avatar-->
                                        <div class="badge badge-light-info">Generated</div>
                                    </div>
                                </td>
                                <td class="text-end">
                                    <a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
                                    <i class="ki-outline ki-down fs-5 ms-1"></i></a>
                                    <!--begin::Menu-->
                                    <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-150px py-4" data-kt-menu="true">

                                        <!--begin::Menu item-->
                                        <div><a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-4"></i>Print/View RDN</a></div>
                                        <!--end::Menu item-->


                                    </div>
                                    <!--end::Menu-->

                                </td>
                            </tr>
                            <tr>
                                <td class="text-start">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">
                                        <input class="form-check-input" type="checkbox" value="1" />
                                    </div>
                                </td>
                                <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                    <a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13760</a>
                                </td>
                                <td class="text-start" data-order="2024-08-07">
                                    <span class="fw-bold  fs-7">07/08/2024</span>
                                </td>
                                <td class="text-start"><span class="fw-bold fs-7">1-Jan-2024 to 31-Dec-2024</span></td>
                                <td class="text-start"> <span class="fw-bold fs-7">TNT.23111 / Safiya Mustapha Isa</span> </td>
                                <td class="text-start"> <span class="fw-bold fs-7">EST.13.166 / No 234, Bonny Street, Lifecamp, Abuja</span> </td>
                                <td class="text-end pe-0">
                                    <span class="fw-bold fs-7">&#8358 2,500,000</span>
                                </td>
                                <td class="text-start" data-order="2024-08-07"><span class="fw-bold  fs-7">07/08/2024</span></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <!--begin:: Avatar -->
                                        <div class="symbol symbol-circle symbol-40px overflow-hidden me-1">
                                                <div class="symbol-label fs-3 bg-light-success text-success">S</div>
                                        </div>
                                        <!--end::Avatar-->
                                        <div class="badge badge-light-success">Sent</div>
                                    </div>
                                </td>
                                <td class="text-end">
                                    <a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
                                    <i class="ki-outline ki-down fs-5 ms-1"></i></a>
                                    <!--begin::Menu-->
                                    <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-150px py-4" data-kt-menu="true">

                                        <!--begin::Menu item-->
                                        <div><a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-4"></i>Print/View RDN</a></div>
                                        <!--end::Menu item-->

                                    </div>
                                    <!--end::Menu-->

                                </td>
                            </tr>
                            <tr>
                                <td class="text-start">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">
                                        <input class="form-check-input" type="checkbox" value="1" />
                                    </div>
                                </td>
                                <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                    <a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13760</a>
                                </td>
                                <td class="text-start" data-order="2024-08-07">
                                    <span class="fw-bold  fs-7">07/08/2024</span>
                                </td>
                                <td class="text-start"><span class="fw-bold fs-7">1-Jan-2024 to 31-Dec-2024</span></td>
                                <td class="text-start"> <span class="fw-bold fs-7">TNT.4533 / Mrs Ngozi Achimugu</span> </td>
                                <td class="text-start"> <span class="fw-bold fs-7">EST.13.444 / No 99, Balale Street, Garki 2, Abuja</span> </td>
                                <td class="text-end pe-0">
                                    <span class="fw-bold fs-7">&#8358 250,000</span>
                                </td>
                                <td class="text-start" data-order="2024-08-07"><span class="fw-bold  fs-7">07/08/2024</span></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <!--begin:: Avatar -->
                                        <div class="symbol symbol-circle symbol-40px overflow-hidden me-1">
                                                <div class="symbol-label fs-3 bg-light-danger text-danger">F</div>
                                        </div>
                                        <!--end::Avatar-->
                                        <div class="badge badge-light-danger">Failed</div>
                                    </div>
                                </td>
                                <td class="text-end">
                                    <a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
                                    <i class="ki-outline ki-down fs-5 ms-1"></i></a>
                                    <!--begin::Menu-->
                                    <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-150px py-4" data-kt-menu="true">

                                        <!--begin::Menu item-->
                                        <div><a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-4"></i>Print/View RDN</a></div>
                                        <!--end::Menu item-->

                                    </div>
                                    <!--end::Menu-->

                                </td>
                            </tr>

                        </tbody>
                    </table>
                    <!--end::Table-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Products-->

            <!--begin::Modal - Add new external xxxxxxxxxxxx-->
            <div class="modal fade" id="kt_modal_add_newexternalmaint" tabindex="-1" aria-hidden="true">

            </div>
            <!--end::Modal - Add xxxxxxx-->



            <!--begin::Modal - Start New Tenacy Allocation Modal-->
            <div class="modal fade" id="kt_modal_new_ticket" tabindex="-1" aria-hidden="true">
                <!--begin::Modal dialog-->
                <div class="modal-dialog modal-dialog-centered mw-500px">

                    <!--begin::Modal content-->
                    <div class="modal-content rounded">
                        <!--begin::Modal header-->
                        <div class="modal-header pb-0 border-0 justify-content-end">
                            <!--begin::Close-->
                            <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                                <i class="ki-outline ki-cross fs-1"></i>
                            </div>
                            <!--end::Close-->
                        </div>
                        <!--begin::Modal header-->
                        <!--begin::Modal body-->
                        <div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-0">
                            <!--begin:Form-->
                            <form id="kt_modal_new_ticket_form" class="form" action="#">

                            <!--begin::Heading-->
                            <div class="mb-13 text-center">
                                <!--begin::Title-->
                                <h1 class="mb-3">Process Rent Demand Notice (RDN)</h1>
                                <!--end::Title-->
                            </div>
                            <!--end::Heading-->

                            </form>
                            <!--end:Form-->
                        </div>
                        <!--end::Modal body-->


                        <div class="d-flex flex-wrap flex-stack m-8 align-items-start">
                            <!--begin::Stat-->
                            <div class="border border-gray-300 border-solid rounded min-w-450px py-3 px-4 me-6 mb-3 w-23">
                                <!--begin::Label-->
                                <span class="badge badge-light-primary">
                                    <div class="fw-semibold fs-6"><i class="ki-outline ki-tablet-down fs-3 text-primary me-2"></i>Select Processing Options:</div>
                                </span>
                                <!--end::Label-->
                                <br><br>


                                    <!--begin::Input group : Process for: -->
                                    <!--begin::Wrapper-->
                                    <div class="d-flex flex-column mb-8 fv-row">
                                        <!--begin::Label-->
                                        <div class="fw-semibold me-5">
                                            <label class="fs-6">Process for:</label>

                                        </div>
                                        <!--end::Label-->
                                        <!--begin::Checkboxes-->
                                        <div class="d-flex align-items-center">
                                            <!--begin::Checkbox-->
                                            <label class="form-check form-check-custom form-check-solid me-10">
                                                <input class="form-check-input h-20px w-20px" type="radio" name="notifications[]" value="email" checked="checked" />
                                                <span class="form-check-label fw-semibold">All</span>
                                            </label>
                                            <!--end::Checkbox-->
                                            <!--begin::Checkbox-->
                                            <label class="form-check form-check-custom form-check-solid me-10">
                                                <input class="form-check-input h-20px w-20px" type="radio" name="notifications[]" value="phone" />
                                                <span class="form-check-label fw-semibold">Selected</span>
                                            </label>
                                            <!--end::Checkbox-->
                                        </div>
                                        <!--end::Checkboxes-->
                                    </div>
                                    <!--end::Wrapper-->


                                    <!--begin::Input group : Notify via: -->
                                    <!--begin::Wrapper-->
                                    <div class="d-flex flex-column mb-8 fv-row">
                                        <!--begin::Label-->
                                        <div class="fw-semibold me-5">
                                            <label class="fs-6">Notify Via:</label>

                                        </div>
                                        <!--end::Label-->
                                        <!--begin::Checkboxes-->
                                        <div class="d-flex align-items-center">
                                            <!--begin::Checkbox-->
                                            <label class="form-check form-check-custom form-check-solid me-10">
                                                <input class="form-check-input h-20px w-20px" type="radio" name="notificationsx[]" value="emailx" checked="checked" />
                                                <span class="form-check-label fw-semibold">Email</span>
                                            </label>
                                            <!--end::Checkbox-->
                                            <!--begin::Checkbox-->
                                            <label class="form-check form-check-custom form-check-solid me-10">
                                                <input class="form-check-input h-20px w-20px" type="radio" name="notificationsx[]" value="phonex" />
                                                <span class="form-check-label fw-semibold">SMS</span>
                                            </label>
                                            <!--end::Checkbox-->
                                        </div>
                                        <!--end::Checkboxes-->
                                    </div>
                                    <!--end::Wrapper-->

                                    <!--begin::Input group : Comment/Notes -->
                                    <div class="d-flex flex-column mb-8 fv-row">
                                        <label class="fs-6 fw-semibold mb-2">Comment/Notes</label>
                                        <textarea class="form-control form-control-solid" rows="2" name="description" placeholder="Type additonal comment/notes here"></textarea>
                                    </div>
                                    <!--end::Input group-->

                                    <!--begin::Input group : Support Docs Attachments-->
                                    <div class="fv-row mb-8">
                                        <label class="fs-6 fw-semibold mb-2">Supporting Docs Attachments</label>
                                        <!--begin::Dropzone-->
                                        <div class="dropzone" id="kt_modal_create_ticket_attachments">
                                            <!--begin::Message-->
                                            <div class="dz-message needsclick align-items-center">
                                                <!--begin::Icon-->
                                                <i class="ki-outline ki-file-up fs-3hx text-primary"></i>
                                                <!--end::Icon-->
                                                <!--begin::Info-->
                                                <div class="ms-4">
                                                    <h3 class="fs-5 fw-bold text-gray-900 mb-1">Drop files here or click to upload.</h3>
                                                    <span class="fw-semibold fs-7 text-gray-500">Upload up to 10 files</span>
                                                </div>
                                                <!--end::Info-->
                                            </div>
                                        </div>
                                        <!--end::Dropzone-->
                                    </div>
                                    <!--end::Input group-->

                                    <a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-file-added fs-2"></i>Process/Generate RDN</a>

                            </div>
                            <!--end::Stat-->
                        </div>

                    </div>
                    <!--end::Modal content-->
                </div>
                <!--end::Modal dialog-->
            </div>
            <!--end::Modal - Start New Internal Maintenance Modal-->




            <!--begin::Modal - Manage Existing External Maintenance Modal-->
            <div class="modal fade" id="kt_modal_manage_internal" tabindex="-1" aria-hidden="true">
                <!--begin::Modal dialog-->
                <div class="modal-dialog modal-dialog-centered mw-1000px">
                    <!--begin::Modal content-->
                    <div class="modal-content rounded">
                        <!--begin::Modal header-->
                        <div class="modal-header pb-0 border-0 justify-content-end">
                            <!--begin::Close-->
                            <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                                <i class="ki-outline ki-cross fs-1"></i>
                            </div>
                            <!--end::Close-->
                        </div>
                        <!--begin::Modal header-->
                        <!--begin::Modal body-->
                        <div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-0">
                            <!--begin:Form-->
                            <form id="kt_modal_new_ticket_form" class="form" action="#">

                            <!--begin::Heading-->
                            <div class="mb-13 text-center">
                                <!--begin::Title-->
                                <h1 class="mb-3">Manage Managing Agent - Estate Allocation: #20240082</h1>
                                <!--end::Title-->
                                <!--begin::Description-->
                                <div class="text-gray-500 fw-semibold fs-5">Perform actions on Managing Agent - Estate Allocation</div>
                                <!--end::Description-->
                            </div>
                            <!--end::Heading-->

                            <!--begin:: Summary Table-->
                            <div class="table-responsive">
                                <table class="table table-bordered table-rounded border-gray-300">
                                    <thead>
                                        <!--begin::Label-->
                                        <span class="badge badge-light-success">
                                            <div class="fw-semibold fs-6"><i class="ki-outline ki-document fs-3 text-success me-2"></i>Proposed Managing Agent - Estate Allocation Details:</div>
                                        </span>
                                        <!--end::Label-->
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td  class="col-md-3"><label class="fs-6 fw-semibold">Entry Date:</label></td>
                                            <td> 1-January-2024</td>
                                        </tr>
                                        <tr>
                                            <td  class="col-md-3"><label class="fs-6 fw-semibold">Estate/Property:</label></td>
                                            <td> EST.13.782 / No 93, Balale Street, Garki 2, Abuja</td>
                                        </tr>
                                        <tr>
                                            <td  class="col-md-3"><label class="fs-6 fw-semibold">Managing Agent:</label></td>
                                            <td> MAG.23198 / Emeka Okafor Realtors Ltd</td>
                                        </tr>
                                        <tr>
                                            <td  class="col-md-3"><label class="fs-6 fw-semibold">MA Fee Rate:</label></td>
                                            <td> 2.5%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!--end:: Summary Table-->


                            </form>
                            <!--end:Form-->
                        </div>
                        <!--end::Modal body-->

                        <div class="d-flex flex-wrap flex-stack m-8 align-items-start">

                            <!--begin::Stat-->
                            <div class="border border-gray-300 border-solid rounded min-w-450px py-3 px-4 me-6 mb-8 w-23">
                                <!--begin::Label-->
                                <span class="badge badge-light-success">
                                    <div class="fw-semibold fs-6"><i class="ki-outline ki-document fs-3 text-success me-2"></i>Action Logs/History:</div>
                                </span>
                                <!--end::Label-->

                                    <div class="d-flex flex-column mb-4 fv-row ">
                                        <!--begin::Card body-->
                                        <div class="card-body pt-0">
                                            <!--begin::Table-->
                                            <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_sales_table">
                                                <thead>
                                                    <tr class="text-start text-gray-600 fw-bold fs-7 text-uppercase gs-0">
                                                        <th class="w-10px">S/N</th>
                                                        <th class="text-start w-20px">Date</th>
                                                        <th class="text-start min-w-50px">Action</th>
                                                        <th class="text-start min-w-50px">Staff</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="fw-semibold text-gray-600">
                                                    <tr>
                                                        <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                                            <span class="fw-normal">1</span>
                                                        </td>
                                                        <td class="text-start" data-order="2024-08-07">
                                                            <span class="fw-normal">07/08/2024</span>
                                                        </td>
                                                        <td class="text-start" data-order="2024-08-11">
                                                            <span class="badge badge-light-warning">Applied for MA</span>
                                                        </td>
                                                        <td class="text-start pe-0">
                                                            <span class="fw-normal">Ibrahim A</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                                            <span class="fw-normal">2</span>
                                                        </td>
                                                        <td class="text-start" data-order="2024-08-07">
                                                            <span class="fw-normal">07/08/2024</span>
                                                        </td>
                                                        <td class="text-start" data-order="2024-08-11">
                                                            <span class="badge badge-light-success">Approved & Escalate</span>
                                                        </td>
                                                        <td class="text-start pe-0">
                                                            <span class="fw-normal">Ibrahim A</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                                            <span class="fw-normal">3</span>
                                                        </td>
                                                        <td class="text-start" data-order="2024-08-07">
                                                            <span class="fw-normal">07/08/2024</span>
                                                        </td>
                                                        <td class="text-start" data-order="2024-08-11">
                                                            <span class="badge badge-light-info">Review Request</span>
                                                        </td>
                                                        <td class="text-start pe-0">
                                                            <span class="fw-normal">Ibrahim A</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                                            <span class="fw-normal">4</span>
                                                        </td>
                                                        <td class="text-start" data-order="2024-08-07">
                                                            <span class="fw-normal">07/08/2024</span>
                                                        </td>
                                                        <td class="text-start" data-order="2024-08-11">
                                                            <span class="badge badge-light-danger">Approved for assignment</span>
                                                        </td>
                                                        <td class="text-start pe-0">
                                                            <div class="fw-normal">EDI</div>
                                                        </td>
                                                    </tr>

                                                </tbody>
                                            </table>
                                            <!--end::Table-->
                                        </div>
                                        <!--end::Card body-->
                                    </div>

                                <br><br>
                                <!-- <a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-2"></i>View/Share/Print</a> -->

                            </div>
                            <!--end::Stat-->

                            <!--begin::Stat-->
                            <div class="border border-gray-300 border-solid rounded min-w-450px py-3 px-4 me-6 mb-3 w-23">
                                <!--begin::Label-->
                                <span class="badge badge-light-primary">
                                    <div class="fw-semibold fs-6"><i class="ki-outline ki-tablet-down fs-3 text-primary me-2"></i>Manage/Update:</div>
                                </span>
                                <!--end::Label-->
                                <br><br>

                                    <!--begin::Input group : Select Action-->
                                    <div class="d-flex flex-column mb-8 fv-row">
                                        <!--begin::Col : Action-->
                                            <label class="required fs-6 fw-semibold mb-2">Action</label>
                                            <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select action" name="product">
                                                <option value="">Select action ...</option>
                                                <option value="1">Upload of KYC documents</option>
                                                <option value="2">Approval/clearance by ME</option>
                                                <option value="3">Approval/clearance by OEC</option>
                                                <option value="4">Generate MEXCOM List</option>
                                                <option value="5">Upload of MEXCOM Approval</option>
                                                <option value="6">Other actions (please type below)</option>
                                            </select>
                                        <!--end::Col-->
                                    </div>
                                    <!--end::Input group-->

                                    <!--begin::Input group : Comment/Notes -->
                                    <div class="d-flex flex-column mb-8 fv-row">
                                        <label class="fs-6 fw-semibold mb-2">Comment/Notes</label>
                                        <textarea class="form-control form-control-solid" rows="2" name="description" placeholder="Type additonal comment/notes here"></textarea>
                                    </div>
                                    <!--end::Input group-->

                                    <!--begin::Input group : Support Docs Attachments-->
                                    <div class="fv-row mb-8">
                                        <label class="fs-6 fw-semibold mb-2">Supporting Docs Attachments</label>
                                        <!--begin::Dropzone-->
                                        <div class="dropzone" id="kt_modal_create_ticket_attachments">
                                            <!--begin::Message-->
                                            <div class="dz-message needsclick align-items-center">
                                                <!--begin::Icon-->
                                                <i class="ki-outline ki-file-up fs-3hx text-primary"></i>
                                                <!--end::Icon-->
                                                <!--begin::Info-->
                                                <div class="ms-4">
                                                    <h3 class="fs-5 fw-bold text-gray-900 mb-1">Drop files here or click to upload.</h3>
                                                    <span class="fw-semibold fs-7 text-gray-500">Upload up to 10 files</span>
                                                </div>
                                                <!--end::Info-->
                                            </div>
                                        </div>
                                        <!--end::Dropzone-->
                                    </div>
                                    <!--end::Input group-->

                                    <!--begin::Input group : Escalate/de-escalate to-->
                                    <div class="d-flex flex-column mb-8 fv-row">
                                        <!--begin::Col : Escalate/de-escalate-->
                                            <label class="required fs-6 fw-semibold mb-2">Escalate/de-escalate to</label>
                                            <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select action" name="product">
                                                <option value="">Select ...</option>
                                                <option value="0">None</option>
                                                <option value="1">OIC</option>
                                                <option value="2">MP</option>
                                                <option value="3">EDI</option>
                                            </select>
                                        <!--end::Col-->
                                    </div>
                                    <!--end::Input group-->


                                    <!--begin::Input group : Notifications SMS Phone In-app -->
                                    <!--begin::Wrapper-->
                                    <div class="d-flex flex-column mb-8 fv-row">
                                        <!--begin::Label-->
                                        <div class="fw-semibold me-5">
                                            <label class="fs-6">Notifications</label>
                                            <div class="fs-7 text-gray-500">Notify Tenant & Agent by Phone, Email or In-app</div>
                                        </div>
                                        <!--end::Label-->
                                        <!--begin::Checkboxes-->
                                        <div class="d-flex align-items-center">
                                            <!--begin::Checkbox-->
                                            <label class="form-check form-check-custom form-check-solid me-10">
                                                <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]" value="email" checked="checked" />
                                                <span class="form-check-label fw-semibold">Email</span>
                                            </label>
                                            <!--end::Checkbox-->
                                            <!--begin::Checkbox-->
                                            <label class="form-check form-check-custom form-check-solid me-10">
                                                <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]" value="phone" />
                                                <span class="form-check-label fw-semibold">Phone</span>
                                            </label>
                                            <!--end::Checkbox-->
                                            <!--begin::Checkbox : In-app -->
                                            <label class="form-check form-check-custom form-check-solid me-10">
                                                <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]" value="inapp" />
                                                <span class="form-check-label fw-semibold">In-app</span>
                                            </label>
                                            <!--end::Checkbox-->
                                        </div>
                                        <!--end::Checkboxes-->
                                    </div>
                                    <!--end::Wrapper-->


                                <a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-file-added fs-2"></i>Save/Commit</a>

                            </div>
                            <!--end::Stat-->

                        </div>





                    </div>
                    <!--end::Modal content-->
                </div>
                <!--end::Modal dialog-->
            </div>
            <!--end::Modal - Manage Existing External Maintenance Modal-->
</x-app-layout>

