@php
    $breadCrumbs = [
        ['name' => __('Home'), 'url' => route('dashboard')],
        ['name' => __('Tenancy')],
        ['name' => __('Tenant Registration/Management.'), 'url' => route('workflows.index')],
    ]
@endphp
<x-app-layout :title="__('Tenant Registration/Management')" :breadCrumbs="$breadCrumbs">
    <!--begin:: Module Dashboard-->
    <div class="d-flex flex-wrap flex-stack mb-8">
        <!--begin::Wrapper-->
        <div class="d-flex flex-column flex-grow-1 pe-8">
            <!--begin::Stats-->
            <div class="d-flex flex-wrap">

                <!--begin::Stat : border-gray-600 border-dashed-->
                <div class="border border-gray-400 rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-primary me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="500" data-kt-countup-prefix="$" data-kt-initialized="1">300</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-primary">
                    <div class="fw-semibold fs-6 ">Total Registered Tenants</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->
                <!--begin::Stat-->
                <div class="border border-gray-400 border-solid rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-success me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="80" data-kt-initialized="1">09</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-success">
                    <div class="fw-semibold fs-6 ">Active Tenants</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->
                <!--begin::Stat-->
                <div class="border border-gray-400 border-solid rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-danger me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="80" data-kt-initialized="1">19</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-danger">
                    <div class="fw-semibold fs-6 ">Inactive Tenants</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->
                <!--begin::Stat-->
                <div class="border border-gray-300 rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-info me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="60" data-kt-countup-prefix="%" data-kt-initialized="1">40</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-info">
                    <div class="fw-semibold fs-6 ">NNPC Staff - Tenants</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->
                <!--begin::Stat-->
                <div class="border border-gray-300 rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <!--begin::Number-->
                    <div class="d-flex align-items-center">
                        <i class="ki-outline ki-arrow-right fs-3 text-info me-2"></i>
                        <div class="fs-1 fw-bold counted" data-kt-countup="true" data-kt-countup-value="60" data-kt-countup-prefix="%" data-kt-initialized="1">10</div>
                    </div>
                    <!--end::Number-->
                    <!--begin::Label-->
                    <span class="badge badge-light-info">
                    <div class="fw-semibold fs-6 ">Third Party - Tenants</div>
                    </span>
                    <!--end::Label-->
                </div>
                <!--end::Stat-->

            </div>
            <!--end::Stats-->
        </div>
        <!--end::Wrapper-->
    </div>
    <!--end:: Module Dashboard-->

    <!--begin::Products-->
    <div class="card card-flush border border-gray-300">
        <!--begin::Card header-->
        <div class="card-header align-items-center py-5 gap-2 gap-md-5">
            <!--begin::Card title-->
            <div class="card-title">
                <!--begin::Search-->
                <div class="d-flex align-items-center position-relative my-1">
                    <i class="ki-outline ki-magnifier fs-3 position-absolute ms-4"></i>
                    <input type="text" data-kt-ecommerce-order-filter="search" class="form-control form-control-solid w-250px ps-12" placeholder="Search" />
                </div>
                <!--end::Search-->
            </div>
            <!--end::Card title-->
            <!--begin::Card toolbar-->
            <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
                <!--begin::Flatpickr-->
                <div class="input-group w-250px">
                    <input class="form-control form-control-solid rounded rounded-end-0" placeholder="Pick date range" id="kt_ecommerce_sales_flatpickr" />
                    <button class="btn btn-icon btn-light" id="kt_ecommerce_sales_flatpickr_clear">
                        <i class="ki-outline ki-cross fs-2"></i>
                    </button>
                </div>
                <!--end::Flatpickr-->
                <div class="w-100 mw-150px">
                    <!--begin::Select2-->
                    <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Status" data-kt-ecommerce-order-filter="status">
                        <option></option>
                        <option value="all">All</option>
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                    </select>
                    <!--end::Select2-->
                </div>


                <!--begin:: Btn add new internal maintenance-->
                <a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_new_ticket" data-bs-toggle="modal"><i class="ki-outline ki-add-files fs-2"></i> Register New Tenant</a>

                <!-- <a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_manage_internal" data-bs-toggle="modal"><i class="ki-outline ki-wrench fs-2"></i> Mgt Modal </a> -->

                <!-- <a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_new_ticket" data-bs-toggle="modal"><i class="ki-outline ki-wrench fs-2"></i> XX </a>
                end::Add product-->
            </div>
            <!--end::Card toolbar-->
        </div>
        <!--end::Card header-->
        <!--begin::Card body-->
        <div class="card-body pt-0">
            <!--begin::Table-->
            <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_sales_table">
                <thead>
                    <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                        <th class="text-start w-10px pe-2">
                            <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                <input class="form-check-input" type="checkbox" data-kt-check="true" data-kt-check-target="#kt_ecommerce_sales_table .form-check-input" value="1" />
                            </div>
                        </th>
                        <th class="min-w-50px">Tenant Code</th>
                        <th class="min-w-50px">Regn. Date</th>
                        <th class="min-w-70px">Full Name</th>
                        <th class="min-w-50px">Phone No</th>
                        <th class="min-w-100px">Company</th>
                        <th class="min-w-40px">Photo</th>
                        <th class="min-w-50px">Type</th>
                        <th class="text-end min-w-70px">Status</th>
                        <th class="text-center min-w-50px">Actions</th>
                    </tr>
                </thead>
                <tbody class="fw-semibold text-gray-600">
                    <tr>
                        <td class="text-start">
                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                <input class="form-check-input" type="checkbox" value="1" />
                            </div>
                        </td>
                        <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                            <a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13760</a>
                        </td>
                        <td class="text-start" data-order="2024-08-07">
                            <span class="fw-bold">07/08/2024</span>
                        </td>
                        <td class="text-start">
                            <span class="fw-bold">Ibrahim Abubakar</span>
                        </td>
                        <td class="text-start"> <span class="fw-bold fs-7">08099887766</span> </td>
                        <td class="text-start"> <span class="fw-bold fs-7">Hutsoft Technologies</span> </td>
                        <td class="text-middle pe-0"><span class="fw-bold">N/A</span></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <!--begin:: Avatar -->
                                <div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
                                        <div class="symbol-label fs-3 bg-light-info text-info">O</div>
                                </div>
                                <!--end::Avatar-->
                                <div class="ms-">
                                    <!--begin::Title-->
                                    <div class="text-gray-800 text-hover-primary fs-5 fw-bold">3rd Party</div>
                                    <!--end::Title-->
                                </div>
                            </div>
                        </td>
                        <td class="text-end pe-0" data-order="Active">
                            <!--begin::Badges-->
                            <div class="badge badge-light-success">Active</div>
                            <!--end::Badges-->
                        </td>
                        <td class="text-end">
                            <a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
                            <i class="ki-outline ki-down fs-5 ms-1"></i></a>
                            <!--begin::Menu-->
                            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">

                                <!--begin::Menu item-->
                                <div><a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_manage_internal" data-bs-toggle="modal"><i class="ki-outline ki-setting-4 fs-4"></i> Manage</a></div>

                                <div class="menu-item px-3">
                                </div>

                                <div><a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-4"></i>Print/View</a></div>
                                <!--end::Menu item-->

                            </div>
                            <!--end::Menu-->

                        </td>
                    </tr>
                    <tr>
                        <td class="text-start">
                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                <input class="form-check-input" type="checkbox" value="1" />
                            </div>
                        </td>
                        <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                            <a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13768</a>
                        </td>
                        <td class="text-start" data-order="2024-07-30">
                            <span class="fw-bold">30/07/2024</span>
                        </td>
                        <td class="text-start">
                            <span class="fw-bold">Adebayo Okama</span>
                        </td>
                        <td class="text-start"> <span class="fw-bold fs-7">08099887766</span> </td>
                        <td class="text-start"> <span class="fw-bold fs-7">N/A</span> </td>
                        <td class="text-middle pe-0"><span class="fw-bold">N/A</span></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <!--begin:: Avatar -->
                                <div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
                                        <div class="symbol-label fs-3 bg-light-success text-success">N</div>
                                </div>
                                <!--end::Avatar-->
                                <div class="ms-">
                                    <!--begin::Title-->
                                    <div class="text-gray-800 text-hover-primary fs-5 fw-bold">NNPC</div>
                                    <!--end::Title-->
                                </div>
                            </div>
                        </td>
                        <td class="text-end pe-0" data-order="Active">
                            <!--begin::Badges-->
                            <div class="badge badge-light-success">Active</div>
                            <!--end::Badges-->
                        </td>
                        <td class="text-end">
                            <a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
                            <i class="ki-outline ki-down fs-5 ms-1"></i></a>
                            <!--begin::Menu-->
                            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">

                                <!--begin::Menu item-->
                                <div><a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_manage_internal" data-bs-toggle="modal"><i class="ki-outline ki-setting-4 fs-4"></i> Manage</a></div>

                                <div class="menu-item px-3">
                                </div>

                                <div><a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-4"></i>Print/View</a></div>
                                <!--end::Menu item-->

                            </div>
                            <!--end::Menu-->

                        </td>
                    </tr>
                    <tr>
                        <td class="text-start">
                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                <input class="form-check-input" type="checkbox" value="1" />
                            </div>
                        </td>
                        <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                            <a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13769</a>
                        </td>
                        <td class="text-start" data-order="2024-07-27">
                            <span class="fw-bold">27/07/2024</span>
                        </td>
                        <td class="text-start">
                            <span class="fw-bold">Yakubu Bobby Lui</span>
                        </td>
                        <td class="text-start"> <span class="fw-bold fs-7">08099887766</span> </td>
                        <td class="text-start"> <span class="fw-bold fs-7">Hutsoft Technologies</span> </td>
                        <td class="text-middle pe-0"><span class="fw-bold">N/A</span></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <!--begin:: Avatar -->
                                <div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
                                        <div class="symbol-label fs-3 bg-light-success text-success">N</div>
                                </div>
                                <!--end::Avatar-->
                                <div class="ms-">
                                    <!--begin::Title-->
                                    <div class="text-gray-800 text-hover-primary fs-5 fw-bold">NNPC</div>
                                    <!--end::Title-->
                                </div>
                            </div>
                        </td>
                        <td class="text-end pe-0" data-order="Inactive">
                            <!--begin::Badges-->
                            <div class="badge badge-light-danger">Inactive</div>
                            <!--end::Badges-->
                        </td>
                        <td class="text-end">
                            <a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
                            <i class="ki-outline ki-down fs-5 ms-1"></i></a>
                            <!--begin::Menu-->
                            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">

                                <!--begin::Menu item-->
                                <div><a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_manage_internal" data-bs-toggle="modal"><i class="ki-outline ki-setting-4 fs-4"></i> Manage</a></div>

                                <div class="menu-item px-3">
                                </div>

                                <div><a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-4"></i>Print/View</a></div>
                                <!--end::Menu item-->

                            </div>
                            <!--end::Menu-->

                        </td>
                    </tr>
                    <tr>
                        <td class="text-start">
                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                <input class="form-check-input" type="checkbox" value="1" />
                            </div>
                        </td>
                        <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                            <a href="apps/ecommerce/sales/details.html" class="text-gray-800 text-hover-primary fw-bold">13770</a>
                        </td>
                        <td class="text-start" data-order="2024-08-01">
                            <span class="fw-bold">01/08/2024</span>
                        </td>
                        <td class="text-start">
                            <span class="fw-bold">Jacob Emeka Isa</span>
                        </td>
                        <td class="text-start"> <span class="fw-bold fs-7">08033223311</span> </td>
                        <td class="text-start"> <span class="fw-bold fs-7">BKB Nigeria Ltd</span> </td>
                        <td class="text-middle pe-0"><span class="fw-bold">N/A</span></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <!--begin:: Avatar -->
                                <div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
                                        <div class="symbol-label fs-3 bg-light-info text-info">O</div>
                                </div>
                                <!--end::Avatar-->
                                <div class="ms-">
                                    <!--begin::Title-->
                                    <div class="text-gray-800 text-hover-primary fs-5 fw-bold">3rd Party</div>
                                    <!--end::Title-->
                                </div>
                            </div>
                        </td>
                        <td class="text-end pe-0" data-order="Inactive">
                            <!--begin::Badges-->
                            <div class="badge badge-light-danger">Inactive</div>
                            <!--end::Badges-->
                        </td>
                        <td class="text-end">
                            <a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
                            <i class="ki-outline ki-down fs-5 ms-1"></i></a>
                            <!--begin::Menu-->
                            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">

                                <!--begin::Menu item-->
                                <div><a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_manage_internal" data-bs-toggle="modal"><i class="ki-outline ki-setting-4 fs-4"></i> Manage</a></div>

                                <div class="menu-item px-3">
                                </div>

                                <div><a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-4"></i>Print/View</a></div>
                                <!--end::Menu item-->

                            </div>
                            <!--end::Menu-->

                        </td>
                    </tr>

                </tbody>
            </table>
            <!--end::Table-->
        </div>
        <!--end::Card body-->
    </div>
    <!--end::Products-->

    <!--begin::Modal - Add new external xxxxxxxxxxxx-->
    <div class="modal fade" id="kt_modal_add_newexternalmaint" tabindex="-1" aria-hidden="true">

    </div>
    <!--end::Modal - Add xxxxxxx-->

    <!--begin::Modal - Start New Internal Maintenance Modal-->
    <div class="modal fade" id="kt_modal_new_ticket" tabindex="-1" aria-hidden="true">
        <!--begin::Modal dialog-->
        <div class="modal-dialog modal-dialog-centered mw-1000px">
            <!--begin::Modal content-->
            <div class="modal-content rounded">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-end">
                    <!--begin::Close-->
                    <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                        <i class="ki-outline ki-cross fs-1"></i>
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-15">
                    <!--begin:Form-->
                    <form id="kt_modal_new_ticket_form" class="form" action="#">

                        <!--begin::Heading-->
                        <div class="mb-13 text-center">
                            <!--begin::Title-->
                            <h1 class="mb-3">Register New Tenant</h1>
                            <!--end::Title-->
                            <!--begin::Description-->
                            <div class="text-gray-500 fw-semibold fs-5">Tenant Registration Form</div>
                            <!--end::Description-->
                        </div>
                        <!--end::Heading-->

                        <div class="badge badge-light-info">PART A: CORPORATE DATA:</div>
                        <!--begin::Input group : Coy Name & Address & Tel-->
                        <div class="row g-9 mb-8">
                            <!--begin::Col : Coy Name-->
                            <div class="col-md-4 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Company/Organisation</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="Enter Name" name="subject" />
                            <!--end::Col-->
                        </div>
                            <!--end::Col-->
                            <!--begin::Col : Address-->
                            <div class="col-md-5 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Address</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="Enter Address" name="subject" />
                            <!--end::Col-->
                        </div>
                            <!--end::Input group-->
                            <!--begin::Col : Tel-->
                            <div class="col-md-3 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Telephone</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="Enter Telephone" name="subject" />
                            <!--end::Col-->
                        </div>
                        </div>

                        <!--begin::Input group : Email, Coy Reg No, Business -->
                        <div class="row g-9 mb-8">
                            <!--end::Col-->
                            <!--begin::Col : Email Address-->
                            <div class="col-md-4 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Email Address</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="Enter Email Address" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Col : Coy Reg No-->
                            <div class="col-md-3 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Regn. No</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="Enter Telephone" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Col-->
                            <!--begin::Col : Business-->
                            <div class="col-md-5 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Nature of Business</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="Enter Email Address" name="subject" />
                            <!--end::Col-->
                        </div>
                            <!--end::Input group-->
                        </div>

                        <br>

                        <div class="badge badge-light-info">PART B: PERSONAL DATA (Company Contact/Representative):</div>
                        <!--begin::Input group : Name, Sex, DOB, Religion-->
                        <div class="row g-9 mb-8">
                            <!--begin::Col : Name-->
                            <div class="col-md-4 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Full Name</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="Enter Name" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Col-->
                            <!--begin::Col : Sex-->
                            <div class="col-md-2 fv-row">
                                <label class="required fs-6 fw-semibold mb-2">Gender</label>
                                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select .." name="product">
                                    <option value="">Select...</option>
                                    <option value="1">Male</option>
                                    <option value="2">Female</option>
                                </select>
                            </div>
                            <!--begin::Col : DOB-->
                            <div class="col-md-3 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Date of Birth</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="Date of Birth" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Col : Religion-->
                            <div class="col-md-3 fv-row">
                                <label class="required fs-6 fw-semibold mb-2">Religion</label>
                                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select religion" name="product">
                                    <option value="">Select...</option>
                                    <option value="1">Christianity</option>
                                    <option value="2">Islam</option>
                                    <option value="3">Others</option>
                                </select>
                            </div>
                        </div>

                        <!--begin::Input group : Marital Status, Nationality, State, LGA-->
                        <div class="row g-9 mb-8">
                            <!--begin::Col : Marital Status-->
                            <div class="col-md-3 fv-row">
                                <label class="required fs-6 fw-semibold mb-2">Marital Status</label>
                                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select .." name="product">
                                    <option value="">Select...</option>
                                    <option value="1">Single</option>
                                    <option value="2">Married</option>
                                    <option value="3">Widowed</option>
                                </select>
                            </div>
                            <!--begin::Col : Nationality-->
                            <div class="col-md-3 fv-row">
                                <label class="required fs-6 fw-semibold mb-2">Nationality</label>
                                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select .." name="product">
                                    <option value="">Select...</option>
                                    <option value="1">Nigerian</option>
                                    <option value="2">Non-Nigerian</option>
                                </select>
                            </div>
                            <!--begin::Col : State-->
                            <div class="col-md-3 fv-row">
                                <label class="required fs-6 fw-semibold mb-2">State</label>
                                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select religion" name="product">
                                    <option value="">Select...</option>
                                    <option value="1">Abuja</option>
                                    <option value="2">Lagos</option>
                                    <option value="3">Kano</option>
                                </select>
                            </div>
                            <!--begin::Col : LGA-->
                            <div class="col-md-3 fv-row">
                                <label class="required fs-6 fw-semibold mb-2">LGA</label>
                                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select LGA" name="product">
                                    <option value="">Select...</option>
                                    <option value="1">Kuje</option>
                                    <option value="2">Abaji</option>
                                    <option value="3">Bwari</option>
                                </select>
                            </div>
                        </div>

                        <!--begin::Input group : Means of ID/No, Email, Res. Address -->
                        <div class="row g-9 mb-8">
                            <!--end::Col-->
                            <!--begin::Col : Means of ID/No-->
                            <div class="col-md-4 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Means of Identification/ID No</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Col : Email-->
                            <div class="col-md-3 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Email Address</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Col-->
                            <!--begin::Col : Res. Address-->
                            <div class="col-md-5 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Residential Address</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Input group-->
                        </div>

                        <!--begin::Input group : Contact Phone No, Name of Employer, Office Address -->
                        <div class="row g-9 mb-8">
                            <!--end::Col-->
                            <!--begin::Col : Contact Phone No-->
                            <div class="col-md-4 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Contact Phone No</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Col : Name of Employer-->
                            <div class="col-md-3 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Name of Employer</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Col-->
                            <!--begin::Col : Res. Address-->
                            <div class="col-md-5 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Office Address</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Input group-->
                        </div>

                        <br>

                        <div class="badge badge-light-info">PART C: NEXT OF KIN DATA (Individual):</div>
                        <!--begin::Input group : Nok Name, Nok Address, , Nok Phone No -->
                        <div class="row g-9 mb-8">
                            <!--end::Col-->
                            <!--begin::Col : Nok Name-->
                            <div class="col-md-4 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Next of KIN Name</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Col : Address-->
                            <div class="col-md-3 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Contact Address</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Col-->
                            <!--begin::Col : Phone No-->
                            <div class="col-md-5 fv-row">
                            <!--begin::Label-->
                                <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                                    <span class="required">Phone No</span>
                                </label>
                                <!--end::Label-->
                                <input type="text" class="form-control form-control-solid" placeholder="" name="subject" />
                            <!--end::Col-->
                            </div>
                            <!--end::Input group-->
                        </div>

                        <br>

                        <div class="badge badge-light-info">PART D: TENANT CATEGORY:</div>
                        <!--begin::Input group : Tenant Category-->
                        <div class="row g-9 mb-8">
                            <!--begin::Col : Nationality-->
                            <div class="col-md-4 fv-row">
                                <label class="required fs-6 fw-semibold mb-2">Tenant Category</label>
                                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select .." name="product">
                                    <option value="">Select...</option>
                                    <option value="1">NNPC Staff</option>
                                    <option value="2">Third Party Tenant</option>
                                </select>
                            </div>
                        </div>


                        <!--begin::Input group : Support Docs Attachments & Passport Photo-->
                        <div class="row g-9 mb-8">

                            <div class="col-md-7 fv-row">
                                <label class="fs-6 fw-semibold mb-2">Related/Supporting Docs Attachments</label>
                                <!--begin::Dropzone-->
                                <div class="dropzone" id="kt_modal_create_ticket_attachments">
                                    <!--begin::Message-->
                                    <div class="dz-message needsclick align-items-center">
                                        <!--begin::Icon-->
                                        <i class="ki-outline ki-file-up fs-3hx text-primary"></i>
                                        <!--end::Icon-->
                                        <!--begin::Info-->
                                        <div class="ms-4">
                                            <h3 class="fs-5 fw-bold text-gray-900 mb-1">Drop files here or click to upload.</h3>
                                            <span class="fw-semibold fs-7 text-gray-500">Upload up to 10 files</span>
                                        </div>
                                        <!--end::Info-->
                                    </div>
                                </div>
                            </div>

                                <div class="col-md-3 fv-row">
                                    <div class="fv-row mb-7">
                                        <!--begin::Label-->
                                        <label class="d-block fw-semibold fs-6 mb-5">Tenant/Company Rep. Photo:</label>
                                        <!--end::Label-->
                                        <!--begin::Image placeholder-->
                                        <style>.image-input-placeholder { background-image: url('/assets/media/svg/files/blank-image.svg'); } [data-bs-theme="dark"] .image-input-placeholder { background-image: url('/assets/media/svg/files/blank-image-dark.svg'); }</style>
                                        <!--end::Image placeholder-->
                                        <!--begin::Image input-->
                                        <div class="image-input image-input-outline image-input-placeholder" data-kt-image-input="true">
                                            <!--begin::Preview existing avatar-->
                                            <div class="image-input-wrapper w-125px h-125px" style="background-image: url(/assets/media/avatars/300-6.jpg);"></div>
                                            <!--end::Preview existing avatar-->
                                            <!--begin::Label-->
                                            <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" aria-label="Change avatar" data-bs-original-title="Change avatar" data-kt-initialized="1">
                                                <i class="ki-outline ki-pencil fs-7"></i>
                                                <!--begin::Inputs-->
                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg">
                                                <input type="hidden" name="avatar_remove">
                                                <!--end::Inputs-->
                                            </label>
                                            <!--end::Label-->
                                            <!--begin::Cancel-->
                                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" aria-label="Cancel avatar" data-bs-original-title="Cancel avatar" data-kt-initialized="1">
                                                <i class="ki-outline ki-cross fs-2"></i>
                                            </span>
                                            <!--end::Cancel-->
                                            <!--begin::Remove-->
                                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" aria-label="Remove avatar" data-bs-original-title="Remove avatar" data-kt-initialized="1">
                                                <i class="ki-outline ki-cross fs-2"></i>
                                            </span>
                                            <!--end::Remove-->
                                        </div>
                                        <!--end::Image input-->
                                        <!--begin::Hint-->
                                        <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                                        <!--end::Hint-->
                                    </div>
                                </div>

                        </div>
                        <!--end::Input group-->

                        <!--begin::Input group : Support Docs Attachments
                        <div class="fv-row mb-8">
                            <label class="fs-6 fw-semibold mb-2">Related/Supporting Docs Attachments</label>

                            <div class="dropzone" id="kt_modal_create_ticket_attachments">

                                <div class="dz-message needsclick align-items-center">

                                    <i class="ki-outline ki-file-up fs-3hx text-primary"></i>

                                    <div class="ms-4">
                                        <h3 class="fs-5 fw-bold text-gray-900 mb-1">Drop files here or click to upload.</h3>
                                        <span class="fw-semibold fs-7 text-gray-500">Upload up to 10 files</span>
                                    </div>

                                </div>
                            </div>

                        </div> -->
                        <!--end::Input group-->


                        <!--begin::Actions : Submit/Cancel Button-->
                        <div class="text-center">
                            <button type="reset" id="kt_modal_new_ticket_cancel" class="btn btn-light me-3">Cancel</button>
                            <button type="submit" id="kt_modal_new_ticket_submit" class="btn btn-primary">
                                <span class="indicator-label">Submit/Commit</span>
                                <span class="indicator-progress">Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                            </button>
                        </div>
                        <!--end::Actions-->

                    </form>
                    <!--end:Form-->
                </div>
                <!--end::Modal body-->
            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - Start New Internal Maintenance Modal-->

    <!--begin::Modal - Manage Existing External Maintenance Modal-->
    <div class="modal fade" id="kt_modal_manage_internal" tabindex="-1" aria-hidden="true">
        <!--begin::Modal dialog-->
        <div class="modal-dialog modal-dialog-centered mw-1000px">
            <!--begin::Modal content-->
            <div class="modal-content rounded">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-end">
                    <!--begin::Close-->
                    <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                        <i class="ki-outline ki-cross fs-1"></i>
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-0">
                    <!--begin:Form-->
                    <form id="kt_modal_new_ticket_form" class="form" action="#">

                    <!--begin::Heading-->
                    <div class="mb-13 text-center">
                        <!--begin::Title-->
                        <h1 class="mb-3">Manage Registered Tenant: #TNT.2282</h1>
                        <!--end::Title-->
                        <!--begin::Description-->
                        <div class="text-gray-500 fw-semibold fs-5">Perform actions on Existing Tenants entry</div>
                        <!--end::Description-->
                    </div>
                    <!--end::Heading-->

                    <!--begin:: Summary Table-->
                    <div class="table-responsive">
                        <table class="table table-bordered table-rounded border-gray-300">
                            <thead>
                                <!--begin::Label-->
                                <span class="badge badge-light-primary">
                                    <div class="fw-semibold fs-6"><i class="ki-outline ki-document fs-3 text-primary me-2"></i>Tenant Summary:</div>
                                </span>
                                <!--end::Label-->
                            </thead>
                            <tbody>
                                <tr>
                                    <td  class="col-md-4"><label class="fs-6 fw-semibold">Registration Date:</label></td>
                                    <td> 1-October-2024</td>
                                </tr>
                                <tr>
                                    <td  class="col-md-3"><label class="fs-6 fw-semibold">Company Name (If corporate):</label></td>
                                    <td> Hutsoft Technologies Ltd</td>
                                </tr>
                                <tr>
                                    <td  class="col-md-3"><label class="fs-6 fw-semibold">Tenant/Rep. Fullname:</label></td>
                                    <td> Abubakar Emeka Bulus</td>
                                </tr>
                                <tr>
                                    <td  class="col-md-3"><label class="fs-6 fw-semibold">Tenant Phone No / Email:</label></td>
                                    <td> 08099887766 / <EMAIL></td>
                                </tr>
                                <tr>
                                    <td  class="col-md-3"><label class="fs-6 fw-semibold">Tenant Type:</label></td>
                                    <td> NNPC Staff</td>
                                </tr>
                                <tr>
                                    <td  class="col-md-3"><label class="fs-6 fw-semibold">Contact Address:</label></td>
                                    <td> No 93, Balale Street, Garki 2, Abuja</td>
                                </tr>
                                <tr>
                                    <td  class="col-md-3"><label class="fs-6 fw-semibold">Tenant Photo:</label></td>
                                    <td> </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                    <!--end:: Summary Table-->

                    </form>
                    <!--end:Form-->
                </div>
                <!--end::Modal body-->

                <div class="d-flex flex-wrap flex-stack m-8 align-items-start">

                    <!--begin::Stat-->
                    <div class="border border-gray-300 border-solid rounded min-w-450px py-3 px-4 me-6 mb-8 w-23">
                        <!--begin::Label-->
                        <span class="badge badge-light-primary">
                            <div class="fw-semibold fs-6"><i class="ki-outline ki-document fs-3 text-primary me-2"></i>Action/Movements History:</div>
                        </span>
                        <!--end::Label-->

                            <div class="d-flex flex-column mb-8 fv-row ">
                                <!--begin::Card body-->
                                <div class="card-body pt-0">
                                    <!--begin::Table-->
                                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_sales_table">
                                        <thead>
                                            <tr class="text-start text-gray-600 fw-bold fs-7 text-uppercase gs-0">
                                                <th class="w-10px">S/N</th>
                                                <th class="text-start w-20px">Date</th>
                                                <th class="text-start min-w-50px">Action</th>
                                                <th class="text-start min-w-50px">Staff</th>
                                            </tr>
                                        </thead>
                                        <tbody class="fw-semibold text-gray-600">
                                            <tr>
                                                <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                                    <span class="fw-normal">1</span>
                                                </td>
                                                <td class="text-start" data-order="2024-08-07">
                                                    <span class="fw-normal">07/08/2024</span>
                                                </td>
                                                <td class="text-start" data-order="2024-08-11">
                                                    <span class="badge badge-light-info">Approved for Review</span>
                                                </td>
                                                <td class="text-start pe-0">
                                                    <span class="fw-normal">Ibrahim A</span>
                                                </td>

                                            </tr>
                                            <tr>
                                                <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                                    <span class="fw-normal">2</span>
                                                </td>
                                                <td class="text-start" data-order="2024-08-07">
                                                    <span class="fw-normal">07/08/2024</span>
                                                </td>
                                                <td class="text-start" data-order="2024-08-11">
                                                    <span class="badge badge-light-info">Approved & Escalate</span>
                                                </td>
                                                <td class="text-start pe-0">
                                                    <span class="fw-normal">Ibrahim A</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                                    <span class="fw-normal">3</span>
                                                </td>
                                                <td class="text-start" data-order="2024-08-07">
                                                    <span class="fw-normal">07/08/2024</span>
                                                </td>
                                                <td class="text-start" data-order="2024-08-11">
                                                    <span class="badge badge-light-info">Reviewed</span>
                                                </td>
                                                <td class="text-start pe-0">
                                                    <span class="fw-normal">Ibrahim A</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="text-start" data-kt-ecommerce-order-filter="order_id">
                                                    <span class="fw-normal">4</span>
                                                </td>
                                                <td class="text-start" data-order="2024-08-07">
                                                    <span class="fw-normal">07/08/2024</span>
                                                </td>
                                                <td class="text-start" data-order="2024-08-11">
                                                    <span class="badge badge-light-info">Closed/KIV</span>
                                                </td>
                                                <td class="text-start pe-0">
                                                    <div class="fw-normal">Ibrahim A</div>
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                    <!--end::Table-->
                                </div>
                                <!--end::Card body-->
                            </div>

                        <br><br>
                        <!-- <a class="btn btn-sm btn-success hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-printer fs-2"></i>View/Share/Print</a> -->

                    </div>
                    <!--end::Stat-->

                    <!--begin::Stat-->
                    <div class="border border-gray-300 border-solid rounded min-w-450px py-3 px-4 me-6 mb-3 w-23">
                        <!--begin::Label-->
                        <span class="badge badge-light-primary">
                            <div class="fw-semibold fs-6"><i class="ki-outline ki-tablet-down fs-3 text-primary me-2"></i>Manage/Update:</div>
                        </span>
                        <!--end::Label-->
                        <br><br>

                            <!--begin::Input group : Select Action-->
                            <div class="d-flex flex-column mb-8 fv-row">
                                <!--begin::Col : Action-->
                                    <label class="required fs-6 fw-semibold mb-2">Action</label>
                                    <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select action" name="product">
                                        <option value="">Select a action ...</option>
                                        <option value="1">Approve & escalate</option>
                                        <option value="2">Re-analyse</option>
                                        <option value="3">Keep in view</option>
                                        <option value="4">Reject & de-escalate</option>
                                        <option value="5">Close process</option>
                                        <option value="6">Other actions</option>
                                    </select>
                                <!--end::Col-->
                            </div>
                            <!--end::Input group-->

                            <!--begin::Input group : Comment/Notes -->
                            <div class="d-flex flex-column mb-8 fv-row">
                                <label class="fs-6 fw-semibold mb-2">Comment/Notes</label>
                                <textarea class="form-control form-control-solid" rows="2" name="description" placeholder="Type additonal comment/notes here"></textarea>
                            </div>
                            <!--end::Input group-->

                            <!--begin::Input group : Support Docs Attachments-->
                            <div class="fv-row mb-8">
                                <label class="fs-6 fw-semibold mb-2">Support Docs Attachments</label>
                                <!--begin::Dropzone-->
                                <div class="dropzone" id="kt_modal_create_ticket_attachments">
                                    <!--begin::Message-->
                                    <div class="dz-message needsclick align-items-center">
                                        <!--begin::Icon-->
                                        <i class="ki-outline ki-file-up fs-3hx text-primary"></i>
                                        <!--end::Icon-->
                                        <!--begin::Info-->
                                        <div class="ms-4">
                                            <h3 class="fs-5 fw-bold text-gray-900 mb-1">Drop files here or click to upload.</h3>
                                            <span class="fw-semibold fs-7 text-gray-500">Upload up to 10 files</span>
                                        </div>
                                        <!--end::Info-->
                                    </div>
                                </div>
                                <!--end::Dropzone-->
                            </div>
                            <!--end::Input group-->

                            <!--begin::Input group : Escalate/de-escalate to-->
                            <div class="d-flex flex-column mb-8 fv-row">
                                <!--begin::Col : Escalate/de-escalate-->
                                    <label class="required fs-6 fw-semibold mb-2">Escalate/de-escalate to</label>
                                    <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select action" name="product">
                                        <option value="">Select ...</option>
                                        <option value="0">None</option>
                                        <option value="1">OIC (Ibrahim Dasuma)</option>
                                        <option value="2">MP (Suleiman Okama)</option>
                                        <option value="3">EDI</option>
                                    </select>
                                <!--end::Col-->
                            </div>
                            <!--end::Input group-->

                            <a class="btn btn-sm btn-primary hover-rotate-end me-2" data-bs-target="#kt_modal_create_app" data-bs-toggle="modal"><i class="ki-outline ki-file-added fs-2"></i>Save/Commit</a>

                    </div>
                    <!--end::Stat-->

                </div>





            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - Manage Existing External Maintenance Modal-->
</x-app-layout>
