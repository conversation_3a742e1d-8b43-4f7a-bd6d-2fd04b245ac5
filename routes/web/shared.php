<?php

use App\Http\Controllers\Shared\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Shared\UserManagement\UserController;
use App\Http\Controllers\Shared\UserManagement\UserRankController;
use App\Http\Controllers\Shared\MenuController;
use App\Http\Controllers\Shared\ModuleController;
use App\Http\Controllers\Shared\SubModuleController;
use App\Http\Controllers\Shared\WorkflowController;
use App\Http\Controllers\Shared\UserManagement\PermissionController;
use App\Http\Controllers\Shared\UserManagement\RoleController;
use App\Http\Controllers\Shared\TenancyManagement\RentDemandNoticeController;
use App\Http\Controllers\Shared\TenancyManagement\TenancyAllocationController;
use App\Http\Controllers\Shared\TenancyManagement\TenancyDashboardController;
use App\Http\Controllers\Shared\TenancyManagement\TenancyManagementController;
use App\Http\Controllers\Shared\TenancyManagement\TenancyRegistrationController;
use App\Http\Controllers\Shared\TenancyManagement\TenantRegistrationController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Shared\NotificationController;

Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])->name('logout');

Route::get('/dashboard', function () {
    return view('dashboard');
})->name('dashboard');
Route::get('/my/', action: function () {
    return view('shared.my.my-profile');
})->name('my-profile');

Route::resource('users', UserController::class)->only(['index', 'store', 'show', 'edit', 'update', 'destroy']);
Route::resource('roles', RoleController::class)->only(['index', 'store', 'show', 'edit', 'update', 'destroy']);
Route::get('roles/{role}/unassign/{user}', [RoleController::class, 'unassign'])->name('roles.unassign');
Route::put('roles/{role}/unassign-many', [RoleController::class, 'unassignMany'])->name('roles.unassign-many');
Route::resource('permissions', PermissionController::class)->only(['index', 'store', 'edit', 'update', 'destroy']);
Route::resource('modules', ModuleController::class)->only(['index', 'store', 'update', 'destroy']);
Route::resource('sub-modules', SubModuleController::class)->only(['index', 'store', 'update', 'destroy']);
Route::resource('menus', MenuController::class);
Route::resource('workflows', WorkflowController::class);
Route::post('workflows/get-actors', [WorkflowController::class, 'getActors'])->name('workflows.get-actors');
Route::post('workflows/get-by-stage', [WorkflowController::class, 'getByStage'])->name('workflows.get-by-stage');
Route::resource('user-ranks', UserRankController::class)->only(['index', 'store', 'show', 'edit', 'update', 'destroy']);
Route::get('user-ranks/{rank}/unassign/{user}', [UserRankController::class, 'unassign'])->name('user-ranks.unassign');
Route::put('user-ranks/{rank}/unassign-many', [UserRankController::class, 'unassignMany'])
    ->name('user-ranks.unassign-many');
Route::post('notifications/mark-as-read-bulk', [NotificationController::class, 'markAsReadBulk'])
    ->name('notifications.markAsReadBulk');
