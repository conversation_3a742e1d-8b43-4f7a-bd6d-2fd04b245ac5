# NotificationService Documentation

## Overview

The `NotificationService` is a comprehensive service for managing notifications in the NPFL Laravel application. It provides methods to send notifications to users based on their roles, designations, ranks, or specific user IDs. The service also includes functionality to read, mark as read, and manage notifications.

## Features

- ✅ Send notifications to users by **roles** (admin, user, etc.)
- ✅ Send notifications to users by **designations** (job positions)
- ✅ Send notifications to users by **ranks** (hierarchical levels)
- ✅ Send notifications to **specific users** by ID
- ✅ **Generic send method** supporting multiple target types
- ✅ Read all notifications for authenticated user
- ✅ Mark specific notification as read
- ✅ Mark all notifications as read
- ✅ Get unread notifications count
- ✅ **Optional messages** - title is required, message is optional
- ✅ Can be called from **anywhere** in the application

## Installation & Setup

The service is already integrated into the application. No additional setup required.

## Usage

### Basic Usage

```php
use App\Services\NotificationService;

// Inject the service
$notificationService = app(NotificationService::class);

// Or use dependency injection in controllers
public function __construct(NotificationService $notificationService)
{
    $this->notificationService = $notificationService;
}
```

### Sending Notifications

#### 1. Send to Roles

```php
// Send to admin roles
$notificationService->sendToRoles(
    ['admin', 'super-admin'],
    'System Alert',
    'The system will be under maintenance tonight.',
    ['priority' => 'high', 'type' => 'maintenance']
);
```

#### 2. Send to Designations

```php
// Send to specific designations (by ID)
$notificationService->sendToDesignations(
    [1, 2, 3], // Designation IDs
    'Department Meeting',
    'Monthly department meeting scheduled for tomorrow.'
);
```

#### 3. Send to Ranks

```php
// Send to specific ranks (by ID)
$notificationService->sendToRanks(
    [1, 2], // Rank IDs for senior positions
    'Board Meeting',
    'Quarterly board meeting next Friday.'
);
```

#### 4. Send to Specific Users

```php
// Send to specific users (by ID)
$notificationService->sendToUsers(
    [1, 5, 10], // User IDs
    'Personal Message',
    'You have been selected for the special project.'
);

// Send to single user
$notificationService->sendToUser(
    5, // User ID
    'Welcome!',
    'Welcome to the platform!'
);
```

#### 5. Generic Send Method (Multiple Targets)

```php
// Send to multiple target types at once
$targets = [
    'roles' => ['admin', 'manager'],
    'designations' => [1, 2],
    'ranks' => [1],
    'users' => [5, 10]
];

$notificationService->send(
    $targets,
    'Important Announcement',
    'This message goes to multiple user groups.',
    [
        'action_url' => url('/announcement'),
        'action_text' => 'View Details'
    ]
);
```

### Reading & Managing Notifications

```php
// Get all notifications for authenticated user
$notifications = $notificationService->notifications();

// Get unread notifications
$unreadNotifications = $notificationService->unreadNotifications();

// Get unread count
$unreadCount = $notificationService->unreadNotificationsCount();

// Mark specific notification as read
$notificationService->markAsRead($notificationId);

// Mark all notifications as read
$notificationService->markAllAsRead();
```

### Helper Methods

```php
// Get users by roles
$adminUsers = $notificationService->getUsersByRoles(['admin']);

// Get users by designations
$managers = $notificationService->getUsersByDesignations([1, 2]);

// Get users by ranks
$seniors = $notificationService->getUsersByRanks([1]);
```

## Usage in Different Contexts

### In Controllers

```php
class AnnouncementController extends Controller
{
    public function sendAnnouncement(Request $request, NotificationService $notificationService)
    {
        $notificationService->sendToRoles(
            ['user'],
            $request->title,
            $request->message
        );
        
        return response()->json(['message' => 'Announcement sent successfully']);
    }
}
```

### In Event Listeners

```php
class SendWelcomeNotification
{
    public function handle(UserRegistered $event)
    {
        app(NotificationService::class)->sendToUser(
            $event->user->id,
            'Welcome!',
            'Thank you for joining our platform.'
        );
    }
}
```

### In Jobs

```php
class SendDailyReportJob implements ShouldQueue
{
    public function handle()
    {
        app(NotificationService::class)->sendToRoles(
            ['admin'],
            'Daily Report',
            'Your daily system report is ready.'
        );
    }
}
```

### In Artisan Commands

```php
// Use the provided command
php artisan notification:send --type=roles --targets=admin --title="System Alert" --message="Maintenance scheduled"
```

## API Reference

### Main Methods

| Method | Parameters | Description |
|--------|------------|-------------|
| `sendToRoles(array $roleSlugs, string $title, string $message = null, array $data = [])` | Role slugs, title, optional message, optional data | Send to users with specific roles |
| `sendToDesignations(array $designationIds, string $title, string $message = null, array $data = [])` | Designation IDs, title, optional message, optional data | Send to users with specific designations |
| `sendToRanks(array $rankIds, string $title, string $message = null, array $data = [])` | Rank IDs, title, optional message, optional data | Send to users with specific ranks |
| `sendToUsers(array $userIds, string $title, string $message = null, array $data = [])` | User IDs, title, optional message, optional data | Send to specific users |
| `sendToUser(int $userId, string $title, string $message = null, array $data = [])` | User ID, title, optional message, optional data | Send to single user |
| `send(array $targets, string $title, string $message = null, array $data = [])` | Target array, title, optional message, optional data | Generic send method |

### Notification Management Methods

| Method | Parameters | Description |
|--------|------------|-------------|
| `notifications()` | None | Get all notifications for authenticated user |
| `unreadNotifications()` | None | Get unread notifications for authenticated user |
| `unreadNotificationsCount()` | None | Get count of unread notifications |
| `markAsRead(string $notificationId)` | Notification ID | Mark specific notification as read |
| `markAllAsRead()` | None | Mark all notifications as read |

## Testing

Run the comprehensive test suite:

```bash
php artisan test tests/Feature/NotificationServiceTest.php
```

## Files Created/Modified

1. **app/Services/NotificationService.php** - Main service implementation
2. **app/Notifications/GenericNotification.php** - Flexible notification class
3. **app/Services/NotificationServiceExample.php** - Usage examples
4. **app/Console/Commands/SendNotificationCommand.php** - Artisan command
5. **tests/Feature/NotificationServiceTest.php** - Comprehensive tests
6. **app/Listeners/SendUserNotification.php** - Fixed existing listener
7. **app/Notifications/WelcomeUserNotification.php** - Fixed existing notification

## Notes

- The service requires an authenticated user for reading notifications
- All send methods return boolean indicating success/failure
- Messages are optional - only title is required
- The service uses Laravel's built-in notification system
- Notifications are stored in the database by default
- The GenericNotification supports email delivery with action buttons
